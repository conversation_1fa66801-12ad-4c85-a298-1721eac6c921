# 贡献指南

感谢你对 Manticore Search 高内聚模块项目的关注！我们欢迎各种形式的贡献。

## 贡献方式

### 🐛 报告 Bug
- 使用 GitHub Issues 报告问题
- 提供详细的错误信息和重现步骤
- 包含系统环境信息

### 💡 提出功能建议
- 在 Issues 中描述新功能需求
- 说明功能的使用场景和价值
- 讨论实现方案的可行性

### 📝 改进文档
- 修正文档中的错误
- 添加使用示例和最佳实践
- 翻译文档到其他语言

### 💻 贡献代码
- 修复 Bug
- 实现新功能
- 优化性能
- 增加测试覆盖

## 开发环境设置

### 前置要求
- Python 3.9+
- Docker 和 Docker Compose
- Git

### 设置步骤
```bash
# 1. Fork 项目到你的 GitHub 账户

# 2. 克隆你的 Fork
git clone https://github.com/YOUR_USERNAME/zhi-manticore2.git
cd zhi-manticore2

# 3. 添加上游仓库
git remote add upstream https://github.com/ORIGINAL_OWNER/zhi-manticore2.git

# 4. 设置开发环境
./setup_manticore_env.sh

# 5. 验证环境
python test_manticore_module.py
```

## 开发流程

### 1. 创建功能分支
```bash
# 从最新的 main 分支创建
git checkout main
git pull upstream main
git checkout -b feature/your-feature-name
```

### 2. 开发和测试
```bash
# 编写代码
# ...

# 运行测试
pytest manticore_search/tests/ -v

# 代码格式化
black manticore_search/
isort manticore_search/

# 代码检查
flake8 manticore_search/
```

### 3. 提交代码
```bash
# 添加变更
git add .

# 提交（使用规范的提交信息）
git commit -m "feat: add new search algorithm"

# 推送到你的 Fork
git push origin feature/your-feature-name
```

### 4. 创建 Pull Request
1. 在 GitHub 上创建 Pull Request
2. 填写 PR 模板
3. 等待代码审查
4. 根据反馈修改代码

## 代码规范

### Python 代码规范
- 遵循 **PEP 8** 编码规范
- 使用 **4 个空格** 缩进
- 行长度限制 **88 字符** (Black 默认)
- 使用 **类型注解**

### 提交信息规范
使用 [Conventional Commits](https://www.conventionalcommits.org/) 格式：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

#### 提交类型
- `feat`: 新功能
- `fix`: Bug 修复
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

#### 示例
```bash
feat: add vector search optimization
fix: resolve connection timeout issue
docs: update API documentation
test: add integration tests for search service
```

### 代码质量检查
```bash
# 格式化代码
black manticore_search/

# 排序导入
isort manticore_search/

# 代码检查
flake8 manticore_search/

# 类型检查（可选）
mypy manticore_search/
```

## 测试指南

### 测试类型
- **单元测试**: 测试单个函数或方法
- **集成测试**: 测试模块间的交互
- **API 测试**: 测试 HTTP 接口
- **性能测试**: 测试系统性能

### 编写测试
```python
import pytest
from manticore_search.services import DocumentService
from manticore_search.models import DocumentCreate

class TestDocumentService:
    @pytest.fixture
    def service(self):
        settings = get_settings()
        return DocumentService(settings)
    
    def test_create_document(self, service):
        # 准备测试数据
        doc_data = DocumentCreate(
            title="测试文档",
            content="测试内容"
        )
        
        # 执行测试
        document = service.create_document(doc_data)
        
        # 验证结果
        assert document.id is not None
        assert document.title == "测试文档"
        
        # 清理
        service.delete_document(document.id)
```

### 运行测试
```bash
# 运行所有测试
pytest manticore_search/tests/ -v

# 运行特定测试
pytest manticore_search/tests/test_document_service.py -v

# 生成覆盖率报告
pytest --cov=manticore_search --cov-report=html

# 并行运行测试
pytest -n auto
```

## 文档规范

### 文档类型
- **API 文档**: 接口说明和示例
- **用户指南**: 使用教程和最佳实践
- **开发文档**: 架构设计和开发指南
- **变更日志**: 版本变更记录

### 文档格式
- 使用 **Markdown** 格式
- 遵循 [中文文案排版指北](https://github.com/sparanoid/chinese-copywriting-guidelines)
- 包含代码示例和使用场景
- 保持文档与代码同步

### 文档结构
```markdown
# 标题

## 概述
简要说明文档内容

## 使用方法
详细的使用说明

### 示例
```python
# 代码示例
```

## 注意事项
重要提醒和限制

## 参考资料
相关链接和资源
```

## Pull Request 指南

### PR 模板
创建 PR 时请填写以下信息：

```markdown
## 变更类型
- [ ] Bug 修复
- [ ] 新功能
- [ ] 文档更新
- [ ] 性能优化
- [ ] 代码重构

## 变更描述
简要描述你的变更内容

## 测试
- [ ] 添加了新的测试用例
- [ ] 所有测试都通过
- [ ] 手动测试通过

## 检查清单
- [ ] 代码遵循项目规范
- [ ] 添加了必要的文档
- [ ] 更新了变更日志
- [ ] 没有引入破坏性变更

## 相关 Issue
关联的 Issue 编号（如果有）
```

### 代码审查
- 所有 PR 都需要至少一个审查者批准
- 审查者会检查代码质量、测试覆盖和文档完整性
- 根据审查意见及时修改代码
- 保持友好和建设性的讨论

## 发布流程

### 版本管理
- 使用 [语义化版本](https://semver.org/lang/zh-CN/)
- 主版本号：不兼容的 API 修改
- 次版本号：向下兼容的功能性新增
- 修订号：向下兼容的问题修正

### 发布步骤
1. 更新版本号
2. 更新变更日志
3. 创建发布标签
4. 发布到包管理器（如果适用）

## 社区准则

### 行为准则
- 尊重所有贡献者
- 保持友好和专业的态度
- 欢迎新手参与
- 提供建设性的反馈

### 沟通方式
- **GitHub Issues**: 报告问题和讨论功能
- **Pull Requests**: 代码审查和讨论
- **Discussions**: 一般性讨论和问答

## 获得帮助

### 常见问题
查看 [FAQ](docs/faq.md) 了解常见问题的解答。

### 联系方式
- **GitHub Issues**: 技术问题和 Bug 报告
- **GitHub Discussions**: 一般性讨论
- **Email**: 私人或敏感问题

### 学习资源
- [FastAPI 文档](https://fastapi.tiangolo.com/)
- [Pydantic 文档](https://pydantic-docs.helpmanual.io/)
- [Manticore Search 文档](https://manual.manticoresearch.com/)
- [pytest 文档](https://docs.pytest.org/)

## 贡献者认可

### 贡献者列表
我们会在项目中认可所有贡献者的努力：
- README 中的贡献者列表
- 变更日志中的贡献记录
- 发布说明中的致谢

### 贡献统计
- 代码贡献
- 文档改进
- 问题报告
- 功能建议
- 社区支持

## 许可证

通过贡献代码，你同意你的贡献将在与项目相同的许可证下发布。

## 致谢

感谢所有为项目做出贡献的开发者！你们的努力让这个项目变得更好。

---

如果你有任何问题或建议，请随时通过 GitHub Issues 联系我们。我们期待你的贡献！
