# Manticore Search 模块

高内聚的企业级搜索引擎模块，基于 Manticore Search 提供全文搜索、向量搜索和混合搜索能力。

## 🎯 模块功能

### 核心特性
- **🔍 全文搜索** - 高性能文本检索和匹配
- **🧠 向量搜索** - 基于 HNSW 算法的语义相似度搜索
- **🔄 混合搜索** - 结合全文和向量搜索的最佳效果
- **📚 文档管理** - 完整的 CRUD 操作和批量处理
- **⚡ 性能优化** - 智能参数调优和实时监控

### 技术架构
```
API层 (FastAPI)          ← REST 接口和路由
    ↓
服务层 (Services)        ← 业务逻辑和规则
    ↓
客户端层 (Client)        ← 数据访问抽象
    ↓
数据库层 (Manticore)     ← 搜索引擎存储
```

## 📁 目录结构

```
manticore_search/
├── api/                 # API 路由和接口
├── clients/             # 数据库客户端
├── models/              # 数据模型定义
├── services/            # 业务逻辑服务
├── utils/               # 工具和配置
├── tests/               # 单元测试
├── docs/                # 详细文档
├── docker-compose.yml   # 服务编排
├── manticore.conf       # 数据库配置
├── test_module.py       # 模块测试
└── run_tests.sh         # 测试脚本
```

## 🚀 快速开始

### 环境要求
- Python 3.9+
- Docker & Docker Compose
- 2GB+ 内存

### 1. 启动服务
```bash
# 启动 Manticore Search 数据库
cd manticore_search
docker-compose up -d

# 等待服务启动
sleep 10
```

### 2. 安装依赖
```bash
# 激活虚拟环境
source ../.venv/bin/activate

# 安装依赖（如果需要）
pip install pydantic pymysql numpy
```

### 3. 运行测试
```bash
# 运行模块测试
./run_tests.sh

# 或手动运行
python3 test_module.py
```

## 💻 使用示例

### 基础导入
```python
from manticore_search import (
    DocumentService,
    SearchService,
    DocumentCreate,
    create_simple_search_request,
    create_vector_search_request,
    create_sample_embedding,
    get_settings
)
```

### 文档管理
```python
# 初始化服务
settings = get_settings()
doc_service = DocumentService(settings)

# 创建文档
document = DocumentCreate(
    title="Python 编程指南",
    content="Python 是一种高级编程语言...",
    category="技术文档",
    embedding=create_sample_embedding("Python 编程")
)

# 存储文档
created_doc = doc_service.create_document(document)
print(f"文档 ID: {created_doc.id}")

# 获取文档
doc = doc_service.get_document(created_doc.id)

# 更新文档
doc_service.update_document(created_doc.id, {"title": "新标题"})

# 删除文档
doc_service.delete_document(created_doc.id)
```

### 全文搜索
```python
# 初始化搜索服务
search_service = SearchService(settings)

# 创建搜索请求
request = create_simple_search_request(
    query="Python 编程",
    limit=10,
    category="技术文档"
)

# 执行搜索
response = search_service.search(request)

# 处理结果
for result in response.results:
    print(f"标题: {result.title}")
    print(f"评分: {result.score}")
    print(f"内容: {result.content[:100]}...")
```

### 向量搜索
```python
# 生成查询向量
query_vector = create_sample_embedding("机器学习算法")

# 创建向量搜索请求
request = create_vector_search_request(
    query="机器学习",
    query_vector=query_vector,
    limit=5
)

# 执行向量搜索
response = search_service.search(request)

# 查看相似度结果
for result in response.results:
    print(f"相似度: {result.score:.3f}")
    print(f"文档: {result.title}")
```

### 混合搜索
```python
from manticore_search import create_hybrid_search_request

# 创建混合搜索请求
query_vector = create_sample_embedding("深度学习")
request = create_hybrid_search_request(
    query="深度学习",
    query_vector=query_vector,
    limit=10
)

# 执行混合搜索（结合全文和向量）
response = search_service.search(request)
```

## 🔧 配置说明

### 环境变量
```bash
# Manticore 连接配置
MANTICORE_HOST=localhost
MANTICORE_PORT=9306
MANTICORE_HTTP_PORT=9308

# 搜索配置
VECTOR_DIMENSIONS=128
KNN_SEARCH_LIMIT=10
```

### 性能优化
```python
# 获取性能摘要
performance = search_service.get_vector_performance_summary()

# 优化向量设置
optimization = search_service.optimize_vector_settings(
    vector_count=10000,
    query_patterns={"avg_qps": 50}
)
```

## 🔗 集成指南

### 在其他模块中使用
```python
# 1. 导入模块
from manticore_search import DocumentService, SearchService

# 2. 初始化服务
from manticore_search import get_settings
settings = get_settings()
search_service = SearchService(settings)

# 3. 使用搜索功能
def search_documents(query: str):
    request = create_simple_search_request(query, limit=20)
    return search_service.search(request)
```

### 错误处理
```python
from manticore_search.utils.exceptions import (
    SearchError,
    VectorSearchError,
    ValidationError
)

try:
    response = search_service.search(request)
except VectorSearchError as e:
    print(f"向量搜索失败: {e}")
except SearchError as e:
    print(f"搜索失败: {e}")
except ValidationError as e:
    print(f"参数验证失败: {e}")
```

## 📚 详细文档

- **[架构文档](docs/architecture.md)** - 系统架构和设计原则
- **[API 文档](docs/api-documentation.md)** - 完整的 API 接口说明
- **[用户指南](docs/user-guide.md)** - 详细的使用指南
- **[开发指南](docs/development-guide.md)** - 开发和贡献指南
- **[测试策略](docs/testing-strategy.md)** - 测试方法和策略

## 🧪 测试验证

### 运行测试
```bash
# 快速测试
./run_tests.sh

# 详细测试
python3 test_module.py
```

### 性能验证
```bash
# 检查服务状态
docker ps | grep manticore

# 测试连接
python3 -c "from manticore_search import get_settings; print('✅ 模块正常')"
```

## 📈 性能指标

- **文档存储**: ~100-200ms/文档
- **全文搜索**: <50ms（中等数据集）
- **向量搜索**: <100ms（10K 向量）
- **混合搜索**: <150ms（结合模式）

## 🤝 贡献指南

参考 [CONTRIBUTING.md](CONTRIBUTING.md) 了解如何贡献代码。

## 📄 许可证

本模块遵循项目整体许可证。
