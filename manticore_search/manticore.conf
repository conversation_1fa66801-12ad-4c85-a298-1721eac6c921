# Manticore Search configuration file - 向量搜索优化版本

searchd {
    listen = 9306:mysql41
    listen = 9308:http
    listen = 9312

    log = /var/log/manticore/searchd.log
    query_log = /var/log/manticore/query.log

    pid_file = /var/run/manticore/searchd.pid

    data_dir = /var/lib/manticore

    # 查询缓存优化
    qcache_max_bytes = 512M
    qcache_thresh_msec = 500
    qcache_ttl_sec = 3600

    # 网络设置优化
    client_timeout = 300
    max_packet_size = 256M

    # 向量搜索优化设置
    # 自动优化配置 - 针对 KNN 索引优化
    auto_optimize = 1

    # 线程和内存优化
    max_children = 0  # 使用所有可用 CPU 核心

    # 查询性能优化
    max_matches = 10000

    # 日志级别
    query_log_format = sphinxql
}
