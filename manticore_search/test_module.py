#!/usr/bin/env python3
"""
Manticore Search 模块测试脚本

测试模块的核心功能：
1. 数据库连接
2. 文档存储
3. 搜索功能
4. 向量优化

Author: <PERSON> (<PERSON>elo<PERSON>)
"""

import sys
import time
import logging
from typing import List, Dict, Any

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目路径
sys.path.append('..')

try:
    from manticore_search import (
        DocumentService,
        SearchService,
        DocumentCreate,
        create_simple_search_request,
        create_vector_search_request,
        create_sample_embedding,
        get_settings
    )
    from manticore_search.clients import ManticoreClient
except ImportError as e:
    logger.error(f"导入模块失败: {e}")
    sys.exit(1)


def test_database_connection():
    """测试数据库连接"""
    logger.info("🔌 测试数据库连接...")
    
    try:
        settings = get_settings()
        client = ManticoreClient(settings)
        
        if client.test_connection():
            logger.info("✅ 数据库连接成功")
            return True
        else:
            logger.error("❌ 数据库连接失败")
            return False
    except Exception as e:
        logger.error(f"❌ 连接测试失败: {e}")
        return False


def test_document_operations():
    """测试文档操作"""
    logger.info("📚 测试文档操作...")
    
    try:
        settings = get_settings()
        doc_service = DocumentService(settings)
        
        # 创建测试文档
        test_doc = DocumentCreate(
            title="测试文档",
            content="这是一个测试文档，用于验证模块功能。",
            category="测试",
            embedding=create_sample_embedding("测试文档内容")
        )
        
        # 存储文档
        created_doc = doc_service.create_document(test_doc)
        logger.info(f"✅ 文档创建成功，ID: {created_doc.id}")
        
        # 清理测试数据
        doc_service.delete_document(created_doc.id)
        logger.info("✅ 测试数据清理完成")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 文档操作测试失败: {e}")
        return False


def test_search_functionality():
    """测试搜索功能"""
    logger.info("🔍 测试搜索功能...")
    
    try:
        settings = get_settings()
        search_service = SearchService(settings)
        
        # 测试全文搜索
        request = create_simple_search_request("测试", limit=5)
        response = search_service.search(request)
        
        logger.info(f"✅ 搜索功能正常，找到 {response.total} 个结果")
        return True
        
    except Exception as e:
        logger.error(f"❌ 搜索功能测试失败: {e}")
        return False


def test_vector_optimization():
    """测试向量优化"""
    logger.info("⚡ 测试向量优化...")
    
    try:
        settings = get_settings()
        search_service = SearchService(settings)
        
        # 获取性能摘要
        performance = search_service.get_vector_performance_summary()
        logger.info(f"✅ 向量优化功能正常: {performance}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 向量优化测试失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    logger.info("🚀 开始 Manticore Search 模块测试")
    logger.info("=" * 50)
    
    tests = [
        ("数据库连接", test_database_connection),
        ("文档操作", test_document_operations),
        ("搜索功能", test_search_functionality),
        ("向量优化", test_vector_optimization),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                logger.info(f"✅ {test_name} 测试通过")
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
    
    logger.info("=" * 50)
    logger.info(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！模块功能正常")
        return True
    else:
        logger.warning(f"⚠️ {total - passed} 项测试失败")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
