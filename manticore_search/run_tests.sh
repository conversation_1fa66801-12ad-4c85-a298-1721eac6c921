#!/bin/bash

# Manticore Search 模块测试运行脚本
# Author: <PERSON> (Develo<PERSON>)

set -e

echo "🧪 Manticore Search 模块测试"
echo "=========================="

# 检查虚拟环境
if [[ "$VIRTUAL_ENV" == "" ]]; then
    echo "激活虚拟环境..."
    if [ -d "../.venv" ]; then
        source ../.venv/bin/activate
    else
        echo "❌ 未找到虚拟环境"
        exit 1
    fi
fi

# 检查 Manticore 服务
if ! nc -z localhost 9306 2>/dev/null; then
    echo "启动 Manticore Search 服务..."
    docker-compose up -d
    sleep 5
fi

# 运行测试
echo "运行模块测试..."
python3 test_module.py

echo "✅ 测试完成"
