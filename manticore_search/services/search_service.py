"""
搜索服务

提供全文搜索、向量搜索和混合搜索功能
"""

import time
import re
from typing import Optional, List, Dict, Any
from ..clients import ManticoreClient
from ..models import (
    SearchRequest,
    SearchResponse,
    SearchResult,
    SearchType,
    DocumentWithScore,
    create_sample_embedding
)
from ..utils import (
    get_service_logger,
    Settings,
    SearchError,
    VectorSearchError,
    ValidationError
)
from ..utils.vector_optimizer import VectorOptimizer, VectorSearchMetrics


class SearchService:
    """搜索服务"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.client = ManticoreClient(settings)
        self.logger = get_service_logger()
        self.table_name = settings.default_table_name
        self.vector_optimizer = VectorOptimizer(settings)
    
    def search(self, request: SearchRequest) -> SearchResponse:
        """执行搜索"""
        start_time = time.time()
        
        try:
            if request.search_type == SearchType.FULLTEXT:
                results = self._fulltext_search(request)
            elif request.search_type == SearchType.VECTOR:
                results = self._vector_search(request)
            elif request.search_type == SearchType.HYBRID:
                results = self._hybrid_search(request)
            else:
                raise ValidationError(f"不支持的搜索类型: {request.search_type}")
            
            # 计算搜索耗时
            took = (time.time() - start_time) * 1000
            
            # 构建响应
            search_results = []
            scores = []
            
            for row in results:
                score = float(row.get('relevance_score', 0))
                scores.append(score)
                
                # 创建文档对象
                document = DocumentWithScore(
                    id=row['id'],
                    title=row['title'],
                    content=row['content'],
                    category=row.get('category', ''),
                    score=score,
                    snippet=row.get('snippet', None),
                    embedding=None  # 不返回向量以节省带宽
                )
                
                search_results.append(SearchResult(
                    document=document,
                    match_info=self._extract_match_info(row)
                ))
            
            # 计算统计信息
            max_score = max(scores) if scores else 0.0
            min_score = min(scores) if scores else 0.0
            avg_score = sum(scores) / len(scores) if scores else 0.0
            
            return SearchResponse(
                results=search_results,
                total=len(results),
                took=took,
                limit=request.limit,
                offset=request.offset,
                has_more=len(results) == request.limit,  # 简化的判断
                search_type=request.search_type,
                query=request.query,
                max_score=max_score,
                min_score=min_score,
                avg_score=avg_score
            )
            
        except Exception as e:
            self.logger.error(f"搜索失败: {e}")
            raise SearchError(f"搜索执行失败: {e}")
    
    def _fulltext_search(self, request: SearchRequest) -> List[Dict[str, Any]]:
        """全文搜索"""
        try:
            # 构建基础查询
            sql = f"""
            SELECT *, WEIGHT() as relevance_score
            FROM {self.table_name}
            WHERE MATCH(%s)
            """
            params = [request.query]
            
            # 添加分类过滤
            if request.category:
                sql += " AND category = %s"
                params.append(request.category)
            
            # 添加最小分数过滤
            if request.min_score:
                sql += " AND WEIGHT() >= %s"
                params.append(request.min_score)
            
            # 添加排序
            sql += " ORDER BY relevance_score DESC"
            
            # 添加分页
            sql += f" LIMIT {request.offset}, {request.limit}"
            
            # 执行查询
            results = self.client.execute_query(sql, tuple(params))
            
            # 如果启用摘要，添加高亮摘要
            if request.enable_snippet and results:
                results = self._add_snippets(results, request)
            
            return results or []
            
        except Exception as e:
            self.logger.error(f"全文搜索失败: {e}")
            raise SearchError(f"全文搜索失败: {e}")
    
    def _vector_search(self, request: SearchRequest) -> List[Dict[str, Any]]:
        """优化的向量搜索"""
        start_time = time.time()

        try:
            if not request.query_vector:
                raise VectorSearchError("向量搜索需要提供查询向量")

            # 验证向量维度
            if len(request.query_vector) != self.settings.vector_dimensions:
                raise VectorSearchError(
                    f"向量维度不匹配: 期望 {self.settings.vector_dimensions}, "
                    f"实际 {len(request.query_vector)}"
                )

            # 估算查询复杂度
            query_complexity = self.vector_optimizer.estimate_query_complexity(
                request.query_vector,
                {"category": request.category, "min_score": getattr(request, 'min_score', None)}
            )

            # 动态计算 ef 参数
            accuracy_requirement = getattr(request, 'accuracy_requirement', 0.8)
            performance_priority = getattr(request, 'performance_priority', 0.5)

            dynamic_ef = self.vector_optimizer.calculate_dynamic_ef(
                query_complexity, accuracy_requirement, performance_priority
            )

            # 向量归一化（提高搜索质量）
            normalized_vector = self._normalize_vector(request.query_vector)

            # 构建 KNN 查询向量字符串
            vector_str = '(' + ','.join(f"{v:.6f}" for v in normalized_vector) + ')'

            # 构建优化的 KNN 查询，使用动态 ef 参数
            base_sql = f"""
            SELECT id, title, content, category,
                   KNN_DIST() as distance,
                   (1.0 - KNN_DIST()) as relevance_score
            FROM {self.table_name}
            WHERE KNN('embedding', {vector_str}, {request.limit * 2}, {dynamic_ef})
            """

            params = []
            conditions = []

            # 添加分类过滤
            if request.category:
                conditions.append("category = %s")
                params.append(request.category)

            # 添加最小相似度过滤
            if hasattr(request, 'min_score') and request.min_score:
                conditions.append("(1.0 - KNN_DIST()) >= %s")
                params.append(request.min_score)

            # 组合条件
            if conditions:
                base_sql += " AND " + " AND ".join(conditions)

            # 添加排序和限制
            final_sql = base_sql + f" ORDER BY distance ASC LIMIT {request.limit}"

            self.logger.debug(f"向量搜索 SQL (ef={dynamic_ef}): {final_sql}")
            self.logger.debug(f"查询参数: {params}")

            # 执行查询
            results = self.client.execute_query(final_sql, tuple(params) if params else None)

            # 后处理结果
            processed_results = []
            for result in results or []:
                # 确保分数在合理范围内
                score = result.get('relevance_score', 0.0)
                if score < 0:
                    score = 0.0
                elif score > 1:
                    score = 1.0

                result['relevance_score'] = score
                processed_results.append(result)

            # 记录性能指标
            query_time = (time.time() - start_time) * 1000  # 转换为毫秒
            metrics = VectorSearchMetrics(
                query_time=query_time,
                recall_rate=len(processed_results) / max(request.limit, 1),  # 简化的召回率计算
                precision_rate=1.0,  # 简化的精确率
                memory_usage=0.0,    # 需要实际测量
                index_size=len(processed_results),
                ef_used=dynamic_ef,
                quantization_type=self.settings.vector_quantization
            )
            self.vector_optimizer.record_metrics(metrics)

            self.logger.info(f"向量搜索返回 {len(processed_results)} 个结果 (用时 {query_time:.2f}ms, ef={dynamic_ef})")
            return processed_results

        except Exception as e:
            self.logger.error(f"向量搜索失败: {e}")
            raise VectorSearchError(f"向量搜索失败: {e}")

    def _normalize_vector(self, vector: List[float]) -> List[float]:
        """向量归一化（L2 范数）"""
        try:
            # 计算向量的 L2 范数
            norm = sum(x * x for x in vector) ** 0.5

            # 避免除零错误
            if norm == 0:
                return [0.0] * len(vector)

            # 归一化
            return [x / norm for x in vector]

        except Exception as e:
            self.logger.warning(f"向量归一化失败: {e}")
            return vector  # 返回原向量
    
    def _hybrid_search(self, request: SearchRequest) -> List[Dict[str, Any]]:
        """混合搜索（全文 + 向量）"""
        try:
            if not request.query_vector:
                raise VectorSearchError("混合搜索需要提供查询向量")
            
            # 获取全文搜索结果
            fulltext_results = self._fulltext_search(request)
            
            # 获取向量搜索结果
            vector_results = self._vector_search(request)
            
            # 合并和重新评分
            combined_results = self._combine_search_results(
                fulltext_results,
                vector_results,
                request.vector_weight
            )
            
            # 重新排序和分页
            combined_results.sort(key=lambda x: x['relevance_score'], reverse=True)
            
            start_idx = request.offset
            end_idx = start_idx + request.limit
            
            return combined_results[start_idx:end_idx]
            
        except Exception as e:
            self.logger.error(f"混合搜索失败: {e}")
            raise SearchError(f"混合搜索失败: {e}")
    
    def _combine_search_results(
        self,
        fulltext_results: List[Dict[str, Any]],
        vector_results: List[Dict[str, Any]],
        vector_weight: float
    ) -> List[Dict[str, Any]]:
        """合并搜索结果"""
        # 创建文档ID到结果的映射
        fulltext_map = {row['id']: row for row in fulltext_results}
        vector_map = {row['id']: row for row in vector_results}
        
        # 获取所有文档ID
        all_doc_ids = set(fulltext_map.keys()) | set(vector_map.keys())
        
        combined_results = []
        
        for doc_id in all_doc_ids:
            fulltext_row = fulltext_map.get(doc_id)
            vector_row = vector_map.get(doc_id)
            
            # 计算组合分数
            fulltext_score = fulltext_row['relevance_score'] if fulltext_row else 0
            vector_score = vector_row['relevance_score'] if vector_row else 0
            
            combined_score = (
                (1 - vector_weight) * fulltext_score +
                vector_weight * vector_score
            )
            
            # 使用全文搜索结果作为基础（包含摘要等信息）
            base_row = fulltext_row or vector_row
            base_row['relevance_score'] = combined_score
            
            combined_results.append(base_row)
        
        return combined_results

    def calculate_vector_similarity(self, vector1: List[float], vector2: List[float]) -> float:
        """计算两个向量的余弦相似度"""
        try:
            if len(vector1) != len(vector2):
                raise ValueError("向量维度不匹配")

            # 计算点积
            dot_product = sum(a * b for a, b in zip(vector1, vector2))

            # 计算向量的模长
            norm1 = sum(a * a for a in vector1) ** 0.5
            norm2 = sum(a * a for a in vector2) ** 0.5

            # 避免除零错误
            if norm1 == 0 or norm2 == 0:
                return 0.0

            # 计算余弦相似度
            similarity = dot_product / (norm1 * norm2)

            # 确保结果在 [-1, 1] 范围内
            return max(-1.0, min(1.0, similarity))

        except Exception as e:
            self.logger.error(f"计算向量相似度失败: {e}")
            return 0.0

    def evaluate_vector_quality(self, vector: List[float]) -> Dict[str, float]:
        """评估向量质量"""
        try:
            if not vector:
                return {"quality_score": 0.0, "norm": 0.0, "sparsity": 1.0}

            # 计算向量的 L2 范数
            norm = sum(x * x for x in vector) ** 0.5

            # 计算稀疏度（零值比例）
            zero_count = sum(1 for x in vector if abs(x) < 1e-8)
            sparsity = zero_count / len(vector)

            # 计算方差（衡量向量的信息丰富度）
            mean_val = sum(vector) / len(vector)
            variance = sum((x - mean_val) ** 2 for x in vector) / len(vector)

            # 综合质量评分
            quality_score = (
                min(norm, 1.0) * 0.4 +  # 范数贡献
                (1 - sparsity) * 0.4 +  # 非稀疏性贡献
                min(variance, 1.0) * 0.2  # 方差贡献
            )

            return {
                "quality_score": quality_score,
                "norm": norm,
                "sparsity": sparsity,
                "variance": variance
            }

        except Exception as e:
            self.logger.error(f"评估向量质量失败: {e}")
            return {"quality_score": 0.0, "norm": 0.0, "sparsity": 1.0}

    def optimize_search_parameters(self, request: SearchRequest) -> SearchRequest:
        """根据查询优化搜索参数"""
        try:
            # 如果有查询向量，评估其质量
            if request.query_vector:
                quality = self.evaluate_vector_quality(request.query_vector)

                # 根据向量质量调整搜索策略
                if quality["quality_score"] < 0.3:
                    # 低质量向量，降低向量权重
                    if hasattr(request, 'vector_weight'):
                        request.vector_weight = min(request.vector_weight, 0.3)
                    self.logger.info(f"检测到低质量向量，调整向量权重为 {request.vector_weight}")

                elif quality["quality_score"] > 0.8:
                    # 高质量向量，可以提高向量权重
                    if hasattr(request, 'vector_weight'):
                        request.vector_weight = max(request.vector_weight, 0.7)
                    self.logger.info(f"检测到高质量向量，调整向量权重为 {request.vector_weight}")

            # 根据查询长度调整策略
            if len(request.query.split()) <= 2:
                # 短查询，更依赖向量搜索
                if hasattr(request, 'vector_weight') and request.query_vector:
                    request.vector_weight = max(request.vector_weight, 0.6)

            return request

        except Exception as e:
            self.logger.warning(f"优化搜索参数失败: {e}")
            return request

    def _add_snippets(
        self, 
        results: List[Dict[str, Any]], 
        request: SearchRequest
    ) -> List[Dict[str, Any]]:
        """添加高亮摘要"""
        try:
            # 使用 Manticore 的 SNIPPET 函数
            snippet_length = request.snippet_length or self.settings.snippet_length
            snippet_around = request.snippet_around or self.settings.snippet_around
            
            for row in results:
                # 构建 SNIPPET 查询
                snippet_sql = f"""
                SELECT SNIPPET(content, %s, 'limit={snippet_length}', 'around={snippet_around}') as snippet
                FROM {self.table_name}
                WHERE id = %s
                """
                
                snippet_result = self.client.execute_query(
                    snippet_sql, 
                    (request.query, row['id'])
                )
                
                if snippet_result:
                    row['snippet'] = snippet_result[0]['snippet']
                else:
                    # 如果 SNIPPET 失败，使用简单的文本截取
                    content = row.get('content', '')
                    if len(content) > snippet_length:
                        row['snippet'] = content[:snippet_length] + '...'
                    else:
                        row['snippet'] = content
            
            return results
            
        except Exception as e:
            self.logger.warning(f"添加摘要失败，使用原始内容: {e}")
            # 如果摘要生成失败，返回原始结果
            return results
    
    def _extract_match_info(self, row: Dict[str, Any]) -> Dict[str, Any]:
        """提取匹配信息"""
        return {
            'score': row.get('relevance_score', 0),
            'has_snippet': 'snippet' in row and row['snippet'] is not None
        }
    
    def suggest(self, query: str, limit: int = 10) -> List[str]:
        """搜索建议（简单实现）"""
        try:
            # 基于现有文档标题生成建议
            sql = f"""
            SELECT DISTINCT title
            FROM {self.table_name}
            WHERE title LIKE %s
            LIMIT {limit}
            """
            
            like_pattern = f"%{query}%"
            results = self.client.execute_query(sql, (like_pattern,))
            
            suggestions = [row['title'] for row in results or []]
            return suggestions
            
        except Exception as e:
            self.logger.error(f"生成搜索建议失败: {e}")
            return []

    def get_vector_performance_summary(self) -> Dict[str, Any]:
        """获取向量搜索性能摘要"""
        return self.vector_optimizer.get_performance_summary()

    def optimize_vector_settings(self, vector_count: int, query_patterns: Dict[str, Any]) -> Dict[str, Any]:
        """优化向量搜索设置"""
        try:
            # 优化 HNSW 参数
            hnsw_config = self.vector_optimizer.optimize_hnsw_parameters(
                vector_count,
                self.settings.vector_dimensions,
                query_patterns
            )

            # 推荐量化方式
            quantization = self.vector_optimizer.recommend_quantization(vector_count)

            return {
                "hnsw_config": {
                    "m": hnsw_config.m,
                    "ef_construction": hnsw_config.ef_construction,
                    "similarity": hnsw_config.similarity.value,
                    "quantization": hnsw_config.quantization.value
                },
                "recommended_quantization": quantization.value,
                "performance_summary": self.get_vector_performance_summary()
            }

        except Exception as e:
            self.logger.error(f"向量设置优化失败: {e}")
            return {"error": str(e)}


if __name__ == "__main__":
    # 测试搜索服务
    from ..utils import get_settings
    from ..models import create_simple_search_request
    
    settings = get_settings()
    service = SearchService(settings)
    
    # 测试全文搜索
    request = create_simple_search_request("测试", 5)
    
    try:
        response = service.search(request)
        print(f"✅ 搜索成功: 找到 {response.total} 个结果")
        print(f"搜索耗时: {response.took:.2f}ms")
        
        for result in response.results:
            print(f"- {result.document.title} (分数: {result.document.score:.2f})")
        
    except Exception as e:
        print(f"❌ 搜索失败: {e}")
