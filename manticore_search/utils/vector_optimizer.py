"""
向量搜索优化器

提供向量搜索的性能优化功能，包括：
- HNSW 参数动态调优
- 向量量化管理
- 动态 ef 参数调整
- 查询性能监控

Author: <PERSON> (Developer)
"""

import time
import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import numpy as np

from ..utils.config import Settings


class QuantizationType(Enum):
    """向量量化类型"""
    NONE = "none"
    BIT_1 = "1bit"
    BIT_4 = "4bit"
    BIT_8 = "8bit"


class SimilarityType(Enum):
    """相似度度量类型"""
    L2 = "l2"
    IP = "ip"
    COSINE = "cosine"


@dataclass
class VectorSearchMetrics:
    """向量搜索性能指标"""
    query_time: float
    recall_rate: float
    precision_rate: float
    memory_usage: float
    index_size: int
    ef_used: int
    quantization_type: str


@dataclass
class HNSWConfig:
    """HNSW 配置参数"""
    m: int = 16
    ef_construction: int = 200
    similarity: SimilarityType = SimilarityType.COSINE
    quantization: QuantizationType = QuantizationType.NONE


class VectorOptimizer:
    """向量搜索优化器"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        self.metrics_history: List[VectorSearchMetrics] = []
        
        # 性能基准
        self.performance_targets = {
            'query_time_ms': 100,  # 目标查询时间 100ms
            'recall_rate': 0.95,   # 目标召回率 95%
            'memory_efficiency': 0.8  # 内存效率目标
        }
    
    def optimize_hnsw_parameters(
        self, 
        vector_count: int, 
        vector_dim: int,
        query_patterns: Dict[str, Any]
    ) -> HNSWConfig:
        """
        根据数据特征和查询模式优化 HNSW 参数
        
        Args:
            vector_count: 向量数量
            vector_dim: 向量维度
            query_patterns: 查询模式统计
            
        Returns:
            优化后的 HNSW 配置
        """
        self.logger.info(f"开始优化 HNSW 参数: 向量数量={vector_count}, 维度={vector_dim}")
        
        # 根据数据规模调整 M 参数
        if vector_count < 10000:
            m = 8  # 小数据集使用较小的 M
        elif vector_count < 100000:
            m = 16  # 中等数据集使用默认 M
        elif vector_count < 1000000:
            m = 32  # 大数据集使用较大的 M
        else:
            m = 48  # 超大数据集使用更大的 M
        
        # 根据维度调整 ef_construction
        if vector_dim <= 64:
            ef_construction = 100
        elif vector_dim <= 128:
            ef_construction = 200
        elif vector_dim <= 256:
            ef_construction = 300
        else:
            ef_construction = 400
        
        # 根据查询频率调整参数
        avg_queries_per_sec = query_patterns.get('avg_qps', 10)
        if avg_queries_per_sec > 100:
            # 高频查询，优化速度
            ef_construction = max(100, ef_construction - 50)
        elif avg_queries_per_sec < 10:
            # 低频查询，优化精度
            ef_construction = min(500, ef_construction + 100)
        
        # 选择相似度度量
        similarity = SimilarityType(self.settings.hnsw_similarity.lower())
        
        config = HNSWConfig(
            m=m,
            ef_construction=ef_construction,
            similarity=similarity,
            quantization=QuantizationType(self.settings.vector_quantization.lower())
        )
        
        self.logger.info(f"HNSW 参数优化完成: M={config.m}, ef_construction={config.ef_construction}")
        return config
    
    def calculate_dynamic_ef(
        self, 
        query_complexity: float,
        accuracy_requirement: float,
        performance_priority: float = 0.5
    ) -> int:
        """
        动态计算 ef 参数
        
        Args:
            query_complexity: 查询复杂度 (0.0-1.0)
            accuracy_requirement: 精度要求 (0.0-1.0)
            performance_priority: 性能优先级 (0.0-1.0, 0为精度优先，1为速度优先)
            
        Returns:
            优化的 ef 值
        """
        if not self.settings.ef_search_auto:
            return self.settings.ef_search_min
        
        # 基础 ef 值
        base_ef = self.settings.ef_search_min
        max_ef = self.settings.ef_search_max
        
        # 根据查询复杂度调整
        complexity_factor = 1.0 + query_complexity * 0.5
        
        # 根据精度要求调整
        accuracy_factor = 1.0 + accuracy_requirement * 0.8
        
        # 根据性能优先级调整
        performance_factor = 1.0 - performance_priority * 0.3
        
        # 计算最终 ef 值
        ef = int(base_ef * complexity_factor * accuracy_factor * performance_factor)
        
        # 限制在合理范围内
        ef = max(self.settings.ef_search_min, min(max_ef, ef))
        
        self.logger.debug(f"动态 ef 计算: 复杂度={query_complexity:.2f}, "
                         f"精度要求={accuracy_requirement:.2f}, "
                         f"性能优先级={performance_priority:.2f}, "
                         f"结果 ef={ef}")
        
        return ef
    
    def estimate_query_complexity(self, query_vector: List[float], filters: Dict[str, Any]) -> float:
        """
        估算查询复杂度
        
        Args:
            query_vector: 查询向量
            filters: 过滤条件
            
        Returns:
            查询复杂度 (0.0-1.0)
        """
        complexity = 0.0
        
        # 向量稀疏度影响复杂度
        if query_vector:
            zero_count = sum(1 for x in query_vector if abs(x) < 1e-8)
            sparsity = zero_count / len(query_vector)
            complexity += sparsity * 0.3  # 稀疏向量查询更复杂
        
        # 过滤条件影响复杂度
        filter_count = len(filters) if filters else 0
        complexity += min(filter_count * 0.2, 0.4)
        
        # 限制在 0-1 范围内
        return min(1.0, complexity)
    
    def recommend_quantization(
        self, 
        vector_count: int, 
        memory_limit_mb: Optional[int] = None
    ) -> QuantizationType:
        """
        推荐向量量化方式
        
        Args:
            vector_count: 向量数量
            memory_limit_mb: 内存限制 (MB)
            
        Returns:
            推荐的量化类型
        """
        # 估算内存使用 (128维 float32 向量)
        vector_size_bytes = self.settings.vector_dimensions * 4  # float32
        total_memory_mb = (vector_count * vector_size_bytes) / (1024 * 1024)
        
        self.logger.info(f"向量内存估算: {total_memory_mb:.1f} MB")
        
        if memory_limit_mb and total_memory_mb > memory_limit_mb:
            # 内存受限，推荐量化
            if total_memory_mb > memory_limit_mb * 4:
                return QuantizationType.BIT_4
            elif total_memory_mb > memory_limit_mb * 2:
                return QuantizationType.BIT_8
            else:
                return QuantizationType.NONE
        
        # 根据数据规模推荐
        if vector_count > 1000000:
            return QuantizationType.BIT_8  # 大数据集使用 8bit 量化
        elif vector_count > 100000:
            return QuantizationType.BIT_8  # 中等数据集可选 8bit
        else:
            return QuantizationType.NONE   # 小数据集不需要量化
    
    def record_metrics(self, metrics: VectorSearchMetrics):
        """记录性能指标"""
        self.metrics_history.append(metrics)
        
        # 保持历史记录在合理范围内
        if len(self.metrics_history) > 1000:
            self.metrics_history = self.metrics_history[-500:]
        
        self.logger.debug(f"记录性能指标: 查询时间={metrics.query_time:.2f}ms, "
                         f"召回率={metrics.recall_rate:.3f}")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.metrics_history:
            return {"status": "no_data"}
        
        recent_metrics = self.metrics_history[-100:]  # 最近100次查询
        
        avg_query_time = sum(m.query_time for m in recent_metrics) / len(recent_metrics)
        avg_recall = sum(m.recall_rate for m in recent_metrics) / len(recent_metrics)
        avg_memory = sum(m.memory_usage for m in recent_metrics) / len(recent_metrics)
        
        return {
            "avg_query_time_ms": avg_query_time,
            "avg_recall_rate": avg_recall,
            "avg_memory_usage_mb": avg_memory,
            "total_queries": len(self.metrics_history),
            "performance_score": self._calculate_performance_score(recent_metrics)
        }
    
    def _calculate_performance_score(self, metrics: List[VectorSearchMetrics]) -> float:
        """计算综合性能评分"""
        if not metrics:
            return 0.0
        
        # 时间评分 (越快越好)
        avg_time = sum(m.query_time for m in metrics) / len(metrics)
        time_score = max(0, 1 - avg_time / self.performance_targets['query_time_ms'])
        
        # 召回率评分
        avg_recall = sum(m.recall_rate for m in metrics) / len(metrics)
        recall_score = avg_recall / self.performance_targets['recall_rate']
        
        # 综合评分
        return (time_score * 0.4 + recall_score * 0.6) * 100
