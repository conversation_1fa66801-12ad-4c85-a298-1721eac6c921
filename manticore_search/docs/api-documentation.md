# Manticore Search API 文档

## API 概述

**基础 URL**: `http://localhost:8000`  
**API 版本**: v1  
**API 前缀**: `/api/v1`  
**内容类型**: `application/json`  
**字符编码**: UTF-8

## 认证

当前版本不需要认证。未来版本将支持 JWT Token 认证。

## 通用响应格式

### 成功响应
```json
{
    "success": true,
    "message": "操作成功",
    "data": { ... },
    "timestamp": "2025-08-13T10:30:00Z"
}
```

### 错误响应
```json
{
    "success": false,
    "message": "操作失败",
    "error_code": "ERROR_CODE",
    "timestamp": "2025-08-13T10:30:00Z"
}
```

## 文档管理 API

### 创建文档
**POST** `/api/v1/documents`

创建新的文档记录。

**请求体**:
```json
{
    "title": "文档标题",
    "content": "文档内容",
    "category": "文档分类",
    "embedding": [0.1, 0.2, 0.3, ...]  // 可选，128维向量
}
```

**响应示例**:
```json
{
    "success": true,
    "message": "文档创建成功",
    "data": {
        "id": 1755057612608276,
        "title": "文档标题",
        "content": "文档内容",
        "category": "文档分类",
        "embedding": [0.1, 0.2, 0.3, ...],
        "created_at": "2025-08-13T10:30:00Z",
        "updated_at": "2025-08-13T10:30:00Z"
    }
}
```

### 获取文档
**GET** `/api/v1/documents/{doc_id}`

根据文档 ID 获取单个文档。

**路径参数**:
- `doc_id` (integer): 文档 ID

**响应示例**:
```json
{
    "success": true,
    "message": "文档获取成功",
    "data": {
        "id": 1755057612608276,
        "title": "文档标题",
        "content": "文档内容",
        "category": "文档分类"
    }
}
```

### 更新文档
**PUT** `/api/v1/documents/{doc_id}`

更新指定文档的信息。

**路径参数**:
- `doc_id` (integer): 文档 ID

**请求体**:
```json
{
    "title": "新标题",        // 可选
    "content": "新内容",      // 可选
    "category": "新分类",     // 可选
    "embedding": [...]       // 可选
}
```

### 删除文档
**DELETE** `/api/v1/documents/{doc_id}`

删除指定的文档。

**路径参数**:
- `doc_id` (integer): 文档 ID

**响应示例**:
```json
{
    "success": true,
    "message": "文档删除成功",
    "data": {
        "deleted": true
    }
}
```

### 列出文档
**GET** `/api/v1/documents`

获取文档列表，支持分页和过滤。

**查询参数**:
- `limit` (integer, 可选): 返回数量，默认 20，最大 100
- `offset` (integer, 可选): 偏移量，默认 0
- `category` (string, 可选): 按分类过滤

**响应示例**:
```json
{
    "success": true,
    "message": "文档列表获取成功",
    "data": [
        {
            "id": 1755057612608276,
            "title": "文档1",
            "content": "内容1",
            "category": "分类1"
        },
        {
            "id": 1755057612608277,
            "title": "文档2",
            "content": "内容2",
            "category": "分类2"
        }
    ]
}
```

### 批量创建文档
**POST** `/api/v1/documents/bulk`

批量创建多个文档。

**请求体**:
```json
{
    "documents": [
        {
            "title": "文档1",
            "content": "内容1",
            "category": "分类1"
        },
        {
            "title": "文档2",
            "content": "内容2",
            "category": "分类2"
        }
    ]
}
```

**响应示例**:
```json
{
    "success": true,
    "message": "批量创建完成",
    "data": {
        "total": 2,
        "success": 2,
        "failed": 0
    }
}
```

## 搜索 API

### 搜索文档
**POST** `/api/v1/search`

执行文档搜索，支持全文搜索、向量搜索和混合搜索。

**请求体**:
```json
{
    "query": "搜索关键词",
    "search_type": "fulltext",     // fulltext, vector, hybrid
    "limit": 20,
    "offset": 0,
    "category": "分类过滤",        // 可选
    "query_vector": [0.1, 0.2, ...], // 向量搜索时必需
    "vector_weight": 0.5,          // 混合搜索权重
    "enable_snippet": true,        // 是否启用高亮摘要
    "snippet_length": 200,         // 摘要长度
    "min_score": 0.1              // 最小相关性分数
}
```

**响应示例**:
```json
{
    "success": true,
    "message": "搜索完成",
    "data": {
        "results": [
            {
                "document": {
                    "id": 1755057612608276,
                    "title": "相关文档",
                    "content": "文档内容...",
                    "category": "分类",
                    "score": 0.95,
                    "snippet": "高亮的<em>搜索</em>摘要..."
                },
                "match_info": {
                    "score": 0.95,
                    "has_snippet": true
                }
            }
        ],
        "total": 1,
        "took": 15.5,
        "limit": 20,
        "offset": 0,
        "has_more": false,
        "search_type": "fulltext",
        "query": "搜索关键词",
        "max_score": 0.95,
        "min_score": 0.95,
        "avg_score": 0.95
    }
}
```

### 搜索建议
**GET** `/api/v1/suggest`

获取搜索建议和自动完成。

**查询参数**:
- `query` (string): 查询文本
- `limit` (integer, 可选): 建议数量，默认 10

**响应示例**:
```json
{
    "success": true,
    "message": "建议获取成功",
    "data": {
        "suggestions": [
            "Python 编程指南",
            "Python 数据分析",
            "Python 机器学习"
        ]
    }
}
```

## 系统 API

### 健康检查
**GET** `/api/v1/health`

检查系统健康状态。

**响应示例**:
```json
{
    "status": "healthy",
    "timestamp": "2025-08-13T10:30:00Z",
    "services": {
        "manticore": {
            "status": "healthy",
            "database_connected": true,
            "response_time_ms": 15.5,
            "version": "6.3.6",
            "uptime_seconds": 3600.0
        },
        "system": {
            "status": "healthy",
            "database_connected": true,
            "response_time_ms": 0.0,
            "version": "CPU: 25.1%, Memory: 45.2%, Disk: 60.3%",
            "uptime_seconds": 86400.0
        },
        "application": {
            "status": "healthy",
            "database_connected": true,
            "response_time_ms": 0.0,
            "version": "1.0.0",
            "uptime_seconds": 3600.0
        }
    }
}
```

### 统计信息
**GET** `/api/v1/stats`

获取系统统计信息。

**响应示例**:
```json
{
    "success": true,
    "message": "统计信息获取成功",
    "data": {
        "total_documents": 1250,
        "total_categories": 15,
        "avg_content_length": 245.6,
        "latest_document_time": null
    }
}
```

### 根路径信息
**GET** `/`

获取 API 基本信息。

**响应示例**:
```json
{
    "name": "Manticore Search API",
    "version": "1.0.0",
    "description": "基于 Manticore Search 的高内聚搜索引擎接口",
    "timestamp": **********.123
}
```

## 错误代码

| 错误代码 | HTTP 状态码 | 描述 |
|----------|-------------|------|
| VALIDATION_ERROR | 400 | 请求参数验证失败 |
| DOCUMENT_NOT_FOUND | 404 | 文档未找到 |
| TABLE_NOT_FOUND | 404 | 数据表不存在 |
| CONNECTION_ERROR | 503 | 数据库连接失败 |
| QUERY_ERROR | 500 | 查询执行失败 |
| SEARCH_ERROR | 500 | 搜索操作失败 |
| VECTOR_SEARCH_ERROR | 500 | 向量搜索失败 |
| TIMEOUT_ERROR | 504 | 操作超时 |
| INTERNAL_ERROR | 500 | 内部服务器错误 |

## 使用示例

### Python 示例
```python
import requests

# 创建文档
doc_data = {
    "title": "Python 编程指南",
    "content": "Python 是一种高级编程语言...",
    "category": "编程"
}

response = requests.post(
    "http://localhost:8000/api/v1/documents",
    json=doc_data
)

if response.status_code == 200:
    result = response.json()
    doc_id = result["data"]["id"]
    print(f"文档创建成功，ID: {doc_id}")

# 搜索文档
search_data = {
    "query": "Python 编程",
    "search_type": "fulltext",
    "limit": 10
}

response = requests.post(
    "http://localhost:8000/api/v1/search",
    json=search_data
)

if response.status_code == 200:
    results = response.json()["data"]["results"]
    for result in results:
        print(f"标题: {result['document']['title']}")
        print(f"分数: {result['document']['score']}")
```

### cURL 示例
```bash
# 创建文档
curl -X POST http://localhost:8000/api/v1/documents \
  -H "Content-Type: application/json" \
  -d '{
    "title": "测试文档",
    "content": "这是一个测试文档",
    "category": "测试"
  }'

# 搜索文档
curl -X POST http://localhost:8000/api/v1/search \
  -H "Content-Type: application/json" \
  -d '{
    "query": "测试",
    "search_type": "fulltext",
    "limit": 5
  }'

# 健康检查
curl http://localhost:8000/api/v1/health
```

## 性能指标

- **API 响应时间**: < 100ms (95th percentile)
- **搜索延迟**: 全文搜索 < 50ms，向量搜索 < 100ms
- **并发支持**: 1000+ QPS
- **数据限制**: 单个文档最大 50KB，批量操作最大 1000 个文档

## 版本历史

| 版本 | 发布日期 | 主要变更 |
|------|----------|----------|
| 1.0.0 | 2025-08-13 | 初始版本发布 |
