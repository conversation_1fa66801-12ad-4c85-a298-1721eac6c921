# Manticore Search 高内聚模块 - 系统架构文档

## 文档信息

**文档版本**: 1.0.0  
**创建日期**: 2025-08-13  
**最后更新**: 2025-08-13  
**架构师**: <PERSON> (Architect)  
**审核状态**: 待审核

## 架构概述

### 设计原则

#### 高内聚低耦合
- **模块内高内聚**: 每个模块内部功能紧密相关，职责单一
- **模块间低耦合**: 模块之间通过明确的接口交互，减少依赖

#### 分层架构
- **表现层 (API Layer)**: FastAPI 路由和请求处理
- **业务层 (Service Layer)**: 核心业务逻辑和规则
- **数据层 (Data Layer)**: 数据访问和持久化
- **基础层 (Infrastructure Layer)**: 工具、配置、日志等

#### 依赖倒置
- 高层模块不依赖低层模块，都依赖于抽象
- 抽象不依赖细节，细节依赖抽象

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    FastAPI Application                      │
├─────────────────────────────────────────────────────────────┤
│                      API Layer                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   Document  │ │   Search    │ │   Health    │          │
│  │   Routes    │ │   Routes    │ │   Routes    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                    Service Layer                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │  Document   │ │   Search    │ │   Health    │          │
│  │  Service    │ │  Service    │ │  Service    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                     Data Layer                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              Manticore Client                          │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                 Infrastructure Layer                       │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   Config    │ │   Logger    │ │ Exceptions  │          │
│  │  Manager    │ │   System    │ │  Handler    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │ Manticore Search│
                    │    Database     │
                    └─────────────────┘
```

## 技术栈

### 核心技术栈

#### 后端框架
- **FastAPI 0.104.1**: 现代、快速的 Web 框架
  - 自动 API 文档生成
  - 类型安全和数据验证
  - 异步支持
  - 依赖注入系统

#### 数据验证
- **Pydantic 2.5.0**: 数据验证和序列化
  - 类型安全的数据模型
  - 自动数据验证
  - JSON 序列化/反序列化
  - 配置管理

#### 数据库连接
- **PyMySQL 1.1.0**: MySQL 协议客户端
  - 纯 Python 实现
  - 支持连接池
  - 事务支持
  - 参数化查询

#### 搜索引擎
- **Manticore Search 6.0+**: 高性能搜索引擎
  - 全文搜索
  - 向量搜索 (KNN)
  - 实时索引
  - SQL 兼容接口

### 开发工具

#### 测试框架
- **pytest 7.4.3**: 测试框架
- **pytest-asyncio 0.21.1**: 异步测试支持
- **httpx 0.25.2**: HTTP 客户端测试

#### 代码质量
- **black 23.11.0**: 代码格式化
- **isort 5.12.0**: 导入排序
- **flake8 6.1.0**: 代码检查

#### 系统监控
- **psutil 5.9.6**: 系统资源监控
- **numpy 1.24.3**: 数值计算（向量操作）

### 部署技术
- **Docker**: 容器化部署
- **Docker Compose**: 多容器编排
- **Uvicorn**: ASGI 服务器

## 模块设计

### 目录结构

```
manticore_search/
├── __init__.py              # 模块入口和导出
├── api/                     # API 层
│   ├── __init__.py
│   └── main.py             # FastAPI 应用和路由
├── services/               # 业务逻辑层
│   ├── __init__.py
│   ├── document_service.py # 文档管理服务
│   ├── search_service.py   # 搜索服务
│   └── health_service.py   # 健康检查服务
├── clients/                # 数据访问层
│   ├── __init__.py
│   └── manticore_client.py # Manticore 客户端
├── models/                 # 数据模型层
│   ├── __init__.py
│   ├── document.py         # 文档模型
│   ├── search.py          # 搜索模型
│   └── response.py        # 响应模型
├── utils/                  # 工具模块
│   ├── __init__.py
│   ├── config.py          # 配置管理
│   ├── logger.py          # 日志系统
│   └── exceptions.py      # 异常定义
├── tests/                  # 测试模块
│   ├── __init__.py
│   ├── test_document_service.py
│   └── test_search_service.py
└── docs/                   # 文档目录
    ├── project-brief.md
    ├── prd.md
    └── architecture.md
```

### API 层设计

#### 路由设计
```python
# 文档管理路由
POST   /api/v1/documents          # 创建文档
GET    /api/v1/documents/{id}     # 获取文档
PUT    /api/v1/documents/{id}     # 更新文档
DELETE /api/v1/documents/{id}     # 删除文档
GET    /api/v1/documents          # 列出文档
POST   /api/v1/documents/bulk     # 批量创建

# 搜索路由
POST   /api/v1/search             # 搜索文档
GET    /api/v1/suggest            # 搜索建议

# 系统路由
GET    /api/v1/health             # 健康检查
GET    /api/v1/stats              # 统计信息
GET    /                          # 根路径信息
```

#### 中间件设计
- **CORS 中间件**: 跨域请求支持
- **异常处理中间件**: 统一异常处理
- **日志中间件**: 请求日志记录
- **性能监控中间件**: 响应时间统计

### 服务层设计

#### DocumentService
```python
class DocumentService:
    """文档管理服务"""
    
    def create_document(self, document: DocumentCreate) -> Document
    def get_document(self, doc_id: int) -> Document
    def update_document(self, doc_id: int, update: DocumentUpdate) -> Document
    def delete_document(self, doc_id: int) -> bool
    def list_documents(self, limit: int, offset: int) -> List[Document]
    def bulk_create_documents(self, bulk: BulkDocumentCreate) -> Dict[str, Any]
    def get_document_stats(self) -> DocumentStats
```

#### SearchService
```python
class SearchService:
    """搜索服务"""
    
    def search(self, request: SearchRequest) -> SearchResponse
    def suggest(self, query: str, limit: int) -> List[str]
    
    # 私有方法
    def _fulltext_search(self, request: SearchRequest) -> List[Dict[str, Any]]
    def _vector_search(self, request: SearchRequest) -> List[Dict[str, Any]]
    def _hybrid_search(self, request: SearchRequest) -> List[Dict[str, Any]]
```

#### HealthService
```python
class HealthService:
    """健康检查服务"""
    
    async def get_health_status(self) -> HealthResponse
    async def check_connection(self) -> bool
    def get_metrics(self) -> Dict[str, Any]
```

### 数据层设计

#### ManticoreClient
```python
class ManticoreClient:
    """Manticore Search 客户端"""
    
    def __init__(self, settings: Settings)
    def test_connection(self) -> bool
    def execute_query(self, sql: str, params: Optional[Tuple]) -> List[Dict]
    def insert_document(self, table: str, document: Dict) -> bool
    def bulk_insert_documents(self, table: str, documents: List[Dict]) -> int
    def search_documents(self, table: str, query: str, **kwargs) -> List[Dict]
    def create_table(self, table: str, schema: str) -> bool
    def table_exists(self, table: str) -> bool
```

### 数据模型设计

#### 文档模型
```python
class DocumentBase(BaseModel):
    title: str
    content: str
    category: Optional[str] = None

class DocumentCreate(DocumentBase):
    embedding: Optional[List[float]] = None

class DocumentUpdate(BaseModel):
    title: Optional[str] = None
    content: Optional[str] = None
    category: Optional[str] = None
    embedding: Optional[List[float]] = None

class Document(DocumentBase):
    id: int
    embedding: Optional[List[float]] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
```

#### 搜索模型
```python
class SearchType(str, Enum):
    FULLTEXT = "fulltext"
    VECTOR = "vector"
    HYBRID = "hybrid"

class SearchRequest(BaseModel):
    query: str
    search_type: SearchType = SearchType.FULLTEXT
    limit: int = 20
    offset: int = 0
    category: Optional[str] = None
    query_vector: Optional[List[float]] = None
    vector_weight: float = 0.5

class SearchResponse(BaseModel):
    results: List[SearchResult]
    total: int
    took: float
    search_type: SearchType
    query: str
```

## 数据流设计

### 文档创建流程
```
Client Request → API Layer → DocumentService → ManticoreClient → Manticore DB
     ↓              ↓             ↓               ↓              ↓
   JSON Data → Validation → Business Logic → SQL Query → Data Storage
     ↑              ↑             ↑               ↑              ↑
Client Response ← API Response ← Service Result ← Query Result ← Stored Data
```

### 搜索查询流程
```
Search Request → API Layer → SearchService → ManticoreClient → Manticore DB
     ↓              ↓           ↓               ↓              ↓
  Query + Filters → Validation → Search Logic → Search Query → Index Search
     ↑              ↑           ↑               ↑              ↑
Search Response ← JSON Format ← Result Process ← Raw Results ← Search Results
```

### 向量搜索流程
```
Vector Query → API Layer → SearchService → ManticoreClient → Manticore DB
     ↓            ↓           ↓               ↓              ↓
Query + Vector → Validation → Vector Logic → KNN Query → Vector Index
     ↑            ↑           ↑               ↑              ↑
Similar Docs ← JSON Format ← Score Process ← KNN Results ← Similarity Calc
```

## 配置管理

### 配置层次
1. **默认配置**: 代码中的默认值
2. **环境变量**: 运行时环境配置
3. **配置文件**: .env 文件配置
4. **命令行参数**: 启动参数覆盖

### 配置分类
```python
class Settings(BaseSettings):
    # Manticore 连接配置
    manticore_host: str = "localhost"
    manticore_port: int = 9306
    manticore_http_port: int = 9308
    
    # 搜索配置
    default_search_limit: int = 20
    max_search_limit: int = 100
    vector_dimensions: int = 128
    
    # API 配置
    api_title: str = "Manticore Search API"
    api_version: str = "1.0.0"
    api_prefix: str = "/api/v1"
    
    # 日志配置
    log_level: str = "INFO"
    
    class Config:
        env_prefix = "MANTICORE_"
```

## 错误处理

### 异常层次
```python
ManticoreSearchError (基础异常)
├── ConnectionError (连接异常)
├── AuthenticationError (认证异常)
├── QueryError (查询异常)
├── ValidationError (验证异常)
├── DocumentNotFoundError (文档未找到)
├── TableNotFoundError (表未找到)
├── SearchError (搜索异常)
│   └── VectorSearchError (向量搜索异常)
├── TimeoutError (超时异常)
└── ResourceLimitError (资源限制异常)
```

### 错误处理策略
- **输入验证**: Pydantic 模型自动验证
- **业务异常**: 自定义异常类型
- **系统异常**: 统一异常处理器
- **错误响应**: 标准化错误格式

## 性能设计

### 性能目标
- **API 响应时间**: < 100ms (95th percentile)
- **搜索延迟**: 全文搜索 < 50ms，向量搜索 < 100ms
- **并发支持**: 1000+ QPS
- **内存使用**: 基础运行 < 512MB

### 性能优化策略
- **连接池**: 数据库连接复用
- **查询优化**: 参数化查询，索引优化
- **缓存策略**: 查询结果缓存（未来实现）
- **异步处理**: FastAPI 异步支持
- **批量操作**: 减少网络往返

## 安全设计

### 安全措施
- **输入验证**: 所有输入严格验证
- **SQL 注入防护**: 参数化查询
- **错误信息**: 不暴露敏感信息
- **日志安全**: 敏感数据脱敏
- **依赖安全**: 定期更新依赖

### 未来安全增强
- **认证授权**: JWT Token 认证
- **API 限流**: 防止滥用
- **HTTPS**: 传输加密
- **审计日志**: 操作审计

## 监控和日志

### 日志设计
- **结构化日志**: JSON 格式输出
- **日志级别**: DEBUG, INFO, WARNING, ERROR, CRITICAL
- **日志轮转**: 按大小和时间轮转
- **彩色输出**: 开发环境友好显示

### 监控指标
- **系统指标**: CPU, 内存, 磁盘使用率
- **应用指标**: API 响应时间, 错误率, QPS
- **业务指标**: 搜索查询数, 文档数量, 用户活跃度

## 部署架构

### 容器化部署
```yaml
# docker-compose.yml
services:
  manticore:
    image: manticoresearch/manticore:latest
    ports:
      - "9306:9306"
      - "9308:9308"
    
  api:
    build: .
    ports:
      - "8000:8000"
    depends_on:
      - manticore
    environment:
      - MANTICORE_HOST=manticore
```

### 扩展部署
- **负载均衡**: Nginx/HAProxy
- **服务发现**: Consul/Eureka
- **配置中心**: Consul/Nacos
- **监控系统**: Prometheus + Grafana

## 变更日志

| 日期 | 版本 | 变更内容 | 负责人 |
|------|------|----------|--------|
| 2025-08-13 | 1.0.0 | 初始架构文档创建 | Winston (Architect) |
