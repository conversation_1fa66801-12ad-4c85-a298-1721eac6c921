# Manticore Search 高内聚模块 - 开发指南

## 开发环境设置

### 系统要求

#### 必需软件
- **Python 3.9+**: 推荐使用 Python 3.11
- **Docker**: 用于运行 Manticore Search
- **Docker Compose**: 用于容器编排
- **Git**: 版本控制

#### 推荐工具
- **VS Code**: 推荐的 IDE
- **Python Extension**: VS Code Python 支持
- **Docker Extension**: VS Code Docker 支持

### 快速开始

#### 1. 克隆项目
```bash
git clone <repository-url>
cd zhi-manticore2
```

#### 2. 创建虚拟环境
```bash
# 创建虚拟环境
python3 -m venv .venv

# 激活虚拟环境
source .venv/bin/activate  # Linux/Mac
# 或
.venv\Scripts\activate     # Windows
```

#### 3. 安装依赖
```bash
pip install -r requirements-manticore.txt
```

#### 4. 启动 Manticore Search
```bash
docker-compose up -d
```

#### 5. 运行测试
```bash
python test_manticore_module.py
```

#### 6. 启动 API 服务
```bash
python run_manticore_api.py --reload
```

### 一键设置脚本
```bash
# 使用自动化设置脚本
chmod +x setup_manticore_env.sh
./setup_manticore_env.sh
```

## 项目结构

### 目录说明
```
manticore_search/
├── __init__.py              # 模块入口，定义公共接口
├── api/                     # FastAPI 应用层
│   ├── __init__.py
│   └── main.py             # 主应用，路由定义
├── services/               # 业务逻辑层
│   ├── __init__.py
│   ├── document_service.py # 文档管理业务逻辑
│   ├── search_service.py   # 搜索业务逻辑
│   └── health_service.py   # 健康检查业务逻辑
├── clients/                # 数据访问层
│   ├── __init__.py
│   └── manticore_client.py # Manticore 数据库客户端
├── models/                 # 数据模型定义
│   ├── __init__.py
│   ├── document.py         # 文档相关模型
│   ├── search.py          # 搜索相关模型
│   └── response.py        # API 响应模型
├── utils/                  # 工具模块
│   ├── __init__.py
│   ├── config.py          # 配置管理
│   ├── logger.py          # 日志系统
│   └── exceptions.py      # 自定义异常
├── tests/                  # 测试代码
│   ├── __init__.py
│   ├── test_document_service.py
│   └── test_search_service.py
└── docs/                   # 项目文档
    ├── project-brief.md
    ├── prd.md
    ├── architecture.md
    └── development-guide.md
```

### 文件命名规范
- **模块文件**: 使用小写字母和下划线 (`document_service.py`)
- **类名**: 使用 PascalCase (`DocumentService`)
- **函数名**: 使用 snake_case (`create_document`)
- **常量**: 使用大写字母和下划线 (`DEFAULT_LIMIT`)
- **私有方法**: 以下划线开头 (`_internal_method`)

## 开发流程

### 分支管理
```bash
# 主分支
main                    # 生产环境代码
develop                 # 开发环境代码

# 功能分支
feature/search-optimization    # 新功能开发
bugfix/vector-search-error    # Bug 修复
hotfix/critical-security-fix  # 紧急修复
```

### 开发步骤
1. **创建功能分支**
   ```bash
   git checkout -b feature/new-feature
   ```

2. **编写代码**
   - 遵循代码规范
   - 添加类型注解
   - 编写文档字符串

3. **编写测试**
   ```bash
   # 创建测试文件
   touch manticore_search/tests/test_new_feature.py
   
   # 运行测试
   pytest manticore_search/tests/test_new_feature.py -v
   ```

4. **代码检查**
   ```bash
   # 格式化代码
   black manticore_search/
   
   # 排序导入
   isort manticore_search/
   
   # 代码检查
   flake8 manticore_search/
   ```

5. **提交代码**
   ```bash
   git add .
   git commit -m "feat: add new search feature"
   git push origin feature/new-feature
   ```

6. **创建 Pull Request**
   - 描述变更内容
   - 关联相关 Issue
   - 请求代码审查

## 代码规范

### Python 代码规范

#### 基本规范
- 遵循 **PEP 8** 编码规范
- 使用 **4 个空格** 缩进，不使用 Tab
- 行长度限制 **88 字符** (Black 默认)
- 使用 **UTF-8** 编码

#### 类型注解
```python
# 函数类型注解
def create_document(self, document: DocumentCreate) -> Document:
    """创建文档"""
    pass

# 变量类型注解
documents: List[Document] = []
config: Optional[Settings] = None

# 复杂类型注解
from typing import Dict, List, Optional, Union

def process_data(
    data: Dict[str, Any], 
    options: Optional[List[str]] = None
) -> Union[str, None]:
    pass
```

#### 文档字符串
```python
def search_documents(
    self, 
    query: str, 
    limit: int = 20,
    search_type: SearchType = SearchType.FULLTEXT
) -> SearchResponse:
    """搜索文档
    
    Args:
        query: 搜索查询字符串
        limit: 返回结果数量限制
        search_type: 搜索类型 (fulltext, vector, hybrid)
    
    Returns:
        SearchResponse: 搜索结果响应对象
    
    Raises:
        SearchError: 搜索执行失败时抛出
        ValidationError: 参数验证失败时抛出
    
    Example:
        >>> service = SearchService(settings)
        >>> response = service.search_documents("Python 编程", limit=10)
        >>> print(f"找到 {response.total} 个结果")
    """
    pass
```

#### 异常处理
```python
# 具体异常处理
try:
    result = self.client.execute_query(sql, params)
except pymysql.err.OperationalError as e:
    self.logger.error(f"数据库连接失败: {e}")
    raise ConnectionError(f"无法连接到数据库: {e}")
except Exception as e:
    self.logger.error(f"未知错误: {e}")
    raise QueryError(f"查询执行失败: {e}")

# 自定义异常
class DocumentNotFoundError(ManticoreSearchError):
    def __init__(self, doc_id: int):
        super().__init__(f"文档 ID {doc_id} 未找到")
        self.doc_id = doc_id
```

### API 设计规范

#### RESTful API 设计
```python
# 资源命名使用复数
GET    /api/v1/documents          # 获取文档列表
POST   /api/v1/documents          # 创建文档
GET    /api/v1/documents/{id}     # 获取单个文档
PUT    /api/v1/documents/{id}     # 更新文档
DELETE /api/v1/documents/{id}     # 删除文档

# 动作使用动词
POST   /api/v1/search             # 搜索动作
GET    /api/v1/health             # 健康检查
```

#### 响应格式标准化
```python
# 成功响应
{
    "success": true,
    "message": "操作成功",
    "data": { ... },
    "timestamp": "2025-08-13T10:30:00Z"
}

# 错误响应
{
    "success": false,
    "message": "操作失败",
    "error_code": "VALIDATION_ERROR",
    "timestamp": "2025-08-13T10:30:00Z"
}
```

## 测试指南

### 测试结构
```
tests/
├── __init__.py
├── test_document_service.py    # 文档服务测试
├── test_search_service.py      # 搜索服务测试
├── test_health_service.py      # 健康检查测试
├── test_manticore_client.py    # 客户端测试
└── conftest.py                 # 测试配置和 fixtures
```

### 测试类型

#### 单元测试
```python
import pytest
from manticore_search.services import DocumentService
from manticore_search.models import DocumentCreate

class TestDocumentService:
    @pytest.fixture
    def service(self):
        settings = get_settings()
        return DocumentService(settings)
    
    def test_create_document(self, service):
        # 准备测试数据
        doc_data = DocumentCreate(
            title="测试文档",
            content="测试内容",
            category="测试"
        )
        
        # 执行测试
        document = service.create_document(doc_data)
        
        # 验证结果
        assert document.id is not None
        assert document.title == "测试文档"
        assert document.content == "测试内容"
        
        # 清理
        service.delete_document(document.id)
```

#### 集成测试
```python
import httpx
import pytest
from fastapi.testclient import TestClient
from manticore_search.api.main import app

class TestDocumentAPI:
    @pytest.fixture
    def client(self):
        return TestClient(app)
    
    def test_create_document_api(self, client):
        # 测试 API 端点
        response = client.post(
            "/api/v1/documents",
            json={
                "title": "API 测试文档",
                "content": "通过 API 创建的测试文档",
                "category": "API测试"
            }
        )
        
        # 验证响应
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "data" in data
```

### 运行测试
```bash
# 运行所有测试
pytest manticore_search/tests/ -v

# 运行特定测试文件
pytest manticore_search/tests/test_document_service.py -v

# 运行特定测试方法
pytest manticore_search/tests/test_document_service.py::TestDocumentService::test_create_document -v

# 生成覆盖率报告
pytest --cov=manticore_search --cov-report=html

# 并行运行测试
pytest -n auto
```

## 调试指南

### 日志调试
```python
# 设置调试日志级别
export MANTICORE_LOG_LEVEL=DEBUG

# 查看详细日志
python run_manticore_api.py --log-level debug
```

### IDE 调试
```python
# VS Code 调试配置 (.vscode/launch.json)
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Debug API Server",
            "type": "python",
            "request": "launch",
            "program": "run_manticore_api.py",
            "args": ["--reload"],
            "console": "integratedTerminal",
            "env": {
                "MANTICORE_LOG_LEVEL": "DEBUG"
            }
        }
    ]
}
```

### 性能调试
```python
# 使用 cProfile 分析性能
python -m cProfile -o profile.stats run_manticore_api.py

# 分析结果
python -c "
import pstats
p = pstats.Stats('profile.stats')
p.sort_stats('cumulative').print_stats(20)
"
```

## 部署指南

### 开发环境部署
```bash
# 启动开发服务器
python run_manticore_api.py --reload --host 0.0.0.0 --port 8000
```

### 生产环境部署
```bash
# 使用 Gunicorn
pip install gunicorn
gunicorn manticore_search.api.main:app -w 4 -k uvicorn.workers.UvicornWorker

# 使用 Docker
docker build -t manticore-search-api .
docker run -p 8000:8000 manticore-search-api
```

### 环境变量配置
```bash
# 创建环境配置文件
cp .env.example .env

# 编辑配置
MANTICORE_HOST=localhost
MANTICORE_PORT=9306
MANTICORE_LOG_LEVEL=INFO
```

## 常见问题

### Q: 如何添加新的搜索功能？
A: 
1. 在 `models/search.py` 中定义新的请求/响应模型
2. 在 `services/search_service.py` 中实现业务逻辑
3. 在 `api/main.py` 中添加新的路由
4. 编写相应的测试用例

### Q: 如何修改数据库表结构？
A: 
1. 修改 `services/document_service.py` 中的 `_create_knowledge_base_table` 方法
2. 更新相关的数据模型
3. 重新创建表或编写迁移脚本
4. 更新测试用例

### Q: 如何添加新的配置项？
A: 
1. 在 `utils/config.py` 的 `Settings` 类中添加新字段
2. 设置默认值和类型注解
3. 更新 `.env.example` 文件
4. 在相关服务中使用新配置

### Q: 如何处理新的异常类型？
A: 
1. 在 `utils/exceptions.py` 中定义新的异常类
2. 在 `api/main.py` 中添加异常处理器
3. 在业务逻辑中抛出相应异常
4. 编写异常处理测试

## 贡献指南

### 代码贡献流程
1. Fork 项目到个人仓库
2. 创建功能分支
3. 编写代码和测试
4. 提交 Pull Request
5. 代码审查和合并

### 代码审查清单
- [ ] 代码遵循项目规范
- [ ] 添加了必要的测试
- [ ] 测试全部通过
- [ ] 文档已更新
- [ ] 没有引入安全问题

## 工具和脚本

### 可用脚本
- `run_manticore_api.py`: 启动 API 服务器
- `test_manticore_module.py`: 运行模块功能测试
- `test_vector_search.py`: 运行向量搜索测试
- `setup_manticore_env.sh`: 一键环境设置

### 开发工具配置
```bash
# 安装开发工具
pip install black isort flake8 mypy

# 配置 pre-commit hooks
pip install pre-commit
pre-commit install
```

## 变更日志

| 日期 | 版本 | 变更内容 | 负责人 |
|------|------|----------|--------|
| 2025-08-13 | 1.0.0 | 初始开发指南创建 | Winston (Architect) |
