# 测试策略

## 测试概述

### 测试目标
- 确保代码质量和功能正确性
- 防止回归错误
- 提高代码可维护性
- 支持重构和优化
- 验证性能指标

### 测试原则
- **测试驱动开发**: 先写测试，再写实现
- **全面覆盖**: 覆盖所有关键功能和边界情况
- **快速反馈**: 测试应该快速执行
- **独立性**: 测试之间不应相互依赖
- **可重复性**: 测试结果应该一致

## 测试分层

### 测试金字塔

```
        /\
       /  \
      /    \     E2E Tests (少量)
     /______\
    /        \
   /          \   Integration Tests (适量)
  /____________\
 /              \
/________________\ Unit Tests (大量)
```

#### 单元测试 (70%)
- **目标**: 测试单个函数或方法
- **范围**: 业务逻辑、数据验证、工具函数
- **特点**: 快速、独立、可重复
- **工具**: pytest

#### 集成测试 (20%)
- **目标**: 测试模块间的交互
- **范围**: 服务层与数据层的集成
- **特点**: 涉及真实的数据库连接
- **工具**: pytest + TestClient

#### 端到端测试 (10%)
- **目标**: 测试完整的用户场景
- **范围**: API 接口的完整流程
- **特点**: 最接近真实使用场景
- **工具**: pytest + httpx

## 测试结构

### 目录组织
```
manticore_search/tests/
├── __init__.py
├── conftest.py                 # 测试配置和 fixtures
├── unit/                       # 单元测试
│   ├── __init__.py
│   ├── test_models.py         # 数据模型测试
│   ├── test_utils.py          # 工具函数测试
│   └── test_exceptions.py     # 异常处理测试
├── integration/                # 集成测试
│   ├── __init__.py
│   ├── test_document_service.py
│   ├── test_search_service.py
│   └── test_health_service.py
├── e2e/                       # 端到端测试
│   ├── __init__.py
│   ├── test_api_endpoints.py
│   └── test_user_scenarios.py
└── fixtures/                  # 测试数据
    ├── sample_documents.json
    └── test_vectors.json
```

### 测试配置 (conftest.py)
```python
import pytest
import asyncio
from fastapi.testclient import TestClient
from manticore_search.api.main import app
from manticore_search.utils import get_settings
from manticore_search.services import DocumentService, SearchService

@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="session")
def settings():
    """测试配置"""
    return get_settings()

@pytest.fixture(scope="session")
def client():
    """API 测试客户端"""
    return TestClient(app)

@pytest.fixture
def document_service(settings):
    """文档服务实例"""
    return DocumentService(settings)

@pytest.fixture
def search_service(settings):
    """搜索服务实例"""
    return SearchService(settings)

@pytest.fixture
def sample_documents():
    """示例文档数据"""
    return [
        {
            "title": "Python 编程指南",
            "content": "Python 是一种高级编程语言...",
            "category": "编程"
        },
        {
            "title": "机器学习基础",
            "content": "机器学习是人工智能的分支...",
            "category": "AI"
        }
    ]
```

## 单元测试

### 数据模型测试
```python
# tests/unit/test_models.py
import pytest
from pydantic import ValidationError
from manticore_search.models import DocumentCreate, SearchRequest

class TestDocumentModels:
    def test_document_create_valid(self):
        """测试有效的文档创建"""
        doc = DocumentCreate(
            title="测试文档",
            content="测试内容",
            category="测试"
        )
        assert doc.title == "测试文档"
        assert doc.content == "测试内容"
        assert doc.category == "测试"
    
    def test_document_create_invalid_title(self):
        """测试无效标题"""
        with pytest.raises(ValidationError):
            DocumentCreate(
                title="",  # 空标题
                content="测试内容"
            )
    
    def test_document_create_long_content(self):
        """测试超长内容"""
        with pytest.raises(ValidationError):
            DocumentCreate(
                title="测试",
                content="x" * 60000  # 超过最大长度
            )

class TestSearchModels:
    def test_search_request_valid(self):
        """测试有效的搜索请求"""
        request = SearchRequest(
            query="测试查询",
            limit=10
        )
        assert request.query == "测试查询"
        assert request.limit == 10
        assert request.search_type == "fulltext"
    
    def test_search_request_invalid_limit(self):
        """测试无效的限制数量"""
        with pytest.raises(ValidationError):
            SearchRequest(
                query="测试",
                limit=1000  # 超过最大限制
            )
```

### 工具函数测试
```python
# tests/unit/test_utils.py
import pytest
from manticore_search.utils import get_settings, get_logger
from manticore_search.utils.exceptions import DocumentNotFoundError

class TestConfig:
    def test_get_settings(self):
        """测试配置获取"""
        settings = get_settings()
        assert settings.manticore_host is not None
        assert settings.manticore_port > 0
        assert settings.vector_dimensions == 128

class TestLogger:
    def test_get_logger(self):
        """测试日志器获取"""
        logger = get_logger("test")
        assert logger.name == "manticore_search.test"

class TestExceptions:
    def test_document_not_found_error(self):
        """测试文档未找到异常"""
        error = DocumentNotFoundError(123)
        assert "123" in str(error)
        assert error.error_code == "DOCUMENT_NOT_FOUND"
        
        error_dict = error.to_dict()
        assert error_dict["error"] == "DocumentNotFoundError"
        assert error_dict["details"]["doc_id"] == 123
```

## 集成测试

### 服务层测试
```python
# tests/integration/test_document_service.py
import pytest
from manticore_search.services import DocumentService
from manticore_search.models import DocumentCreate, DocumentUpdate
from manticore_search.utils.exceptions import DocumentNotFoundError

class TestDocumentService:
    def test_document_lifecycle(self, document_service):
        """测试文档完整生命周期"""
        # 创建文档
        doc_data = DocumentCreate(
            title="集成测试文档",
            content="这是集成测试的内容",
            category="测试"
        )
        
        created_doc = document_service.create_document(doc_data)
        assert created_doc.id is not None
        assert created_doc.title == doc_data.title
        
        # 获取文档
        retrieved_doc = document_service.get_document(created_doc.id)
        assert retrieved_doc.id == created_doc.id
        assert retrieved_doc.title == created_doc.title
        
        # 更新文档
        update_data = DocumentUpdate(title="更新后的标题")
        updated_doc = document_service.update_document(created_doc.id, update_data)
        assert updated_doc.title == "更新后的标题"
        
        # 删除文档
        success = document_service.delete_document(created_doc.id)
        assert success is True
        
        # 验证删除
        with pytest.raises(DocumentNotFoundError):
            document_service.get_document(created_doc.id)
    
    def test_bulk_operations(self, document_service, sample_documents):
        """测试批量操作"""
        from manticore_search.models import BulkDocumentCreate, DocumentCreate
        
        # 准备批量数据
        documents = [DocumentCreate(**doc) for doc in sample_documents]
        bulk_data = BulkDocumentCreate(documents=documents)
        
        # 批量创建
        result = document_service.bulk_create_documents(bulk_data)
        assert result['total'] == len(sample_documents)
        assert result['success'] == len(sample_documents)
        assert result['failed'] == 0
        
        # 清理数据
        docs = document_service.list_documents(limit=100)
        for doc in docs:
            if doc.category in ["编程", "AI"]:
                document_service.delete_document(doc.id)
```

### 搜索服务测试
```python
# tests/integration/test_search_service.py
import pytest
from manticore_search.services import SearchService, DocumentService
from manticore_search.models import create_simple_search_request

class TestSearchService:
    @pytest.fixture(autouse=True)
    def setup_test_data(self, document_service, sample_documents):
        """设置测试数据"""
        self.created_docs = []
        
        for doc_data in sample_documents:
            from manticore_search.models import DocumentCreate
            doc = document_service.create_document(DocumentCreate(**doc_data))
            self.created_docs.append(doc)
        
        yield
        
        # 清理测试数据
        for doc in self.created_docs:
            try:
                document_service.delete_document(doc.id)
            except:
                pass
    
    def test_fulltext_search(self, search_service):
        """测试全文搜索"""
        request = create_simple_search_request("Python", limit=10)
        response = search_service.search(request)
        
        assert response.total > 0
        assert response.search_type == "fulltext"
        assert response.took > 0
        
        # 验证结果包含查询词
        found_python = False
        for result in response.results:
            if "Python" in result.document.title or "Python" in result.document.content:
                found_python = True
                break
        assert found_python
    
    def test_search_with_filters(self, search_service):
        """测试带过滤条件的搜索"""
        from manticore_search.models import SearchRequest
        
        request = SearchRequest(
            query="学习",
            category="AI",
            limit=10
        )
        
        response = search_service.search(request)
        
        # 验证所有结果都属于指定分类
        for result in response.results:
            assert result.document.category == "AI"
```

## 端到端测试

### API 端点测试
```python
# tests/e2e/test_api_endpoints.py
import pytest
from fastapi.testclient import TestClient

class TestDocumentAPI:
    def test_document_crud_workflow(self, client):
        """测试文档 CRUD 完整流程"""
        # 创建文档
        create_data = {
            "title": "API 测试文档",
            "content": "这是通过 API 创建的测试文档",
            "category": "API测试"
        }
        
        response = client.post("/api/v1/documents", json=create_data)
        assert response.status_code == 200
        
        result = response.json()
        assert result["success"] is True
        doc_id = result["data"]["id"]
        
        # 获取文档
        response = client.get(f"/api/v1/documents/{doc_id}")
        assert response.status_code == 200
        
        result = response.json()
        assert result["data"]["title"] == create_data["title"]
        
        # 更新文档
        update_data = {"title": "更新后的标题"}
        response = client.put(f"/api/v1/documents/{doc_id}", json=update_data)
        assert response.status_code == 200
        
        # 搜索文档
        search_data = {
            "query": "API 测试",
            "search_type": "fulltext",
            "limit": 10
        }
        
        response = client.post("/api/v1/search", json=search_data)
        assert response.status_code == 200
        
        result = response.json()
        assert result["data"]["total"] > 0
        
        # 删除文档
        response = client.delete(f"/api/v1/documents/{doc_id}")
        assert response.status_code == 200

class TestSystemAPI:
    def test_health_check(self, client):
        """测试健康检查"""
        response = client.get("/api/v1/health")
        assert response.status_code == 200
        
        result = response.json()
        assert result["status"] in ["healthy", "unhealthy"]
        assert "services" in result
    
    def test_root_endpoint(self, client):
        """测试根端点"""
        response = client.get("/")
        assert response.status_code == 200
        
        result = response.json()
        assert "name" in result
        assert "version" in result
```

## 性能测试

### 响应时间测试
```python
# tests/performance/test_performance.py
import pytest
import time
from manticore_search.services import SearchService
from manticore_search.models import create_simple_search_request

class TestPerformance:
    def test_search_response_time(self, search_service):
        """测试搜索响应时间"""
        request = create_simple_search_request("测试", limit=20)
        
        start_time = time.time()
        response = search_service.search(request)
        end_time = time.time()
        
        response_time_ms = (end_time - start_time) * 1000
        
        # 验证响应时间 < 100ms
        assert response_time_ms < 100
        assert response.took < 100
    
    def test_concurrent_searches(self, search_service):
        """测试并发搜索"""
        import concurrent.futures
        
        def perform_search():
            request = create_simple_search_request("测试", limit=10)
            return search_service.search(request)
        
        # 并发执行 10 个搜索
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(perform_search) for _ in range(10)]
            results = [future.result() for future in futures]
        
        # 验证所有搜索都成功
        assert len(results) == 10
        for result in results:
            assert result.total >= 0
```

## 测试数据管理

### 测试数据隔离
```python
# tests/conftest.py
@pytest.fixture(scope="function")
def clean_database():
    """清理数据库"""
    # 测试前清理
    yield
    # 测试后清理
    # 实现数据清理逻辑
```

### 测试数据工厂
```python
# tests/factories.py
from manticore_search.models import DocumentCreate

class DocumentFactory:
    @staticmethod
    def create_sample_document(title="测试文档", category="测试"):
        return DocumentCreate(
            title=title,
            content=f"这是 {title} 的内容",
            category=category
        )
    
    @staticmethod
    def create_batch_documents(count=5):
        return [
            DocumentFactory.create_sample_document(f"文档 {i}", f"分类 {i % 3}")
            for i in range(count)
        ]
```

## 测试执行

### 本地测试
```bash
# 运行所有测试
pytest manticore_search/tests/ -v

# 运行特定类型的测试
pytest manticore_search/tests/unit/ -v
pytest manticore_search/tests/integration/ -v
pytest manticore_search/tests/e2e/ -v

# 生成覆盖率报告
pytest --cov=manticore_search --cov-report=html --cov-report=term

# 并行运行测试
pytest -n auto

# 运行性能测试
pytest manticore_search/tests/performance/ -v
```

### CI/CD 测试
```yaml
# .github/workflows/test.yml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      manticore:
        image: manticoresearch/manticore:latest
        ports:
          - 9306:9306
          - 9308:9308
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.11
    
    - name: Install dependencies
      run: |
        pip install -r requirements-manticore.txt
    
    - name: Run tests
      run: |
        pytest manticore_search/tests/ -v --cov=manticore_search
```

## 测试指标

### 覆盖率目标
- **总体覆盖率**: > 80%
- **核心业务逻辑**: > 90%
- **API 端点**: 100%
- **异常处理**: > 85%

### 性能指标
- **单元测试**: < 1ms 平均执行时间
- **集成测试**: < 100ms 平均执行时间
- **API 测试**: < 200ms 平均执行时间
- **搜索响应**: < 100ms (95th percentile)

## 测试最佳实践

### 编写原则
1. **AAA 模式**: Arrange, Act, Assert
2. **单一职责**: 每个测试只验证一个功能点
3. **描述性命名**: 测试名称应该清楚描述测试内容
4. **独立性**: 测试之间不应相互依赖
5. **可重复性**: 测试结果应该一致

### 常见陷阱
- 测试依赖外部服务
- 测试数据污染
- 过度模拟（Mock）
- 测试逻辑过于复杂
- 忽略边界条件

### 维护策略
- 定期审查和更新测试
- 删除过时的测试
- 重构重复的测试代码
- 保持测试与代码同步
- 监控测试执行时间

## 变更日志

| 日期 | 版本 | 变更内容 | 负责人 |
|------|------|----------|--------|
| 2025-08-13 | 1.0.0 | 初始测试策略文档创建 | Winston (Architect) |
