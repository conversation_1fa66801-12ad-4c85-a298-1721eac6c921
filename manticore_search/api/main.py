"""
FastAPI 主应用

提供 Manticore Search 的 REST API 接口
"""

import time
from contextlib import asynccontextmanager
from fastapi import FastAPI, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from ..services import DocumentService, SearchService, HealthService
from ..models import (
    DocumentCreate,
    DocumentUpdate,
    BulkDocumentCreate,
    SearchRequest,
    ApiResponse,
    HealthResponse,
    create_success_response,
    create_error_response
)
from ..utils import get_settings, get_api_logger, ManticoreSearchError

# 全局服务实例
document_service = None
search_service = None
health_service = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global document_service, search_service, health_service
    
    logger = get_api_logger()
    logger.info("🚀 启动 Manticore Search API 服务...")
    
    # 初始化服务
    settings = get_settings()
    document_service = DocumentService(settings)
    search_service = SearchService(settings)
    health_service = HealthService(settings)
    
    # 验证连接
    if await health_service.check_connection():
        logger.info("✅ Manticore 连接验证成功")
    else:
        logger.error("❌ Manticore 连接验证失败")
        raise Exception("无法连接到 Manticore Search")
    
    yield
    
    logger.info("🛑 关闭 Manticore Search API 服务...")


# 创建 FastAPI 应用
def create_app() -> FastAPI:
    """创建 FastAPI 应用实例"""
    settings = get_settings()
    
    app = FastAPI(
        title=settings.api_title,
        description=settings.api_description,
        version=settings.api_version,
        lifespan=lifespan
    )
    
    # 添加 CORS 中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 添加全局异常处理
    @app.exception_handler(ManticoreSearchError)
    async def manticore_exception_handler(request, exc: ManticoreSearchError):
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content=create_error_response(
                message=exc.message,
                error_code=exc.error_code
            ).dict()
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request, exc: Exception):
        logger = get_api_logger()
        logger.error(f"未处理的异常: {exc}")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=create_error_response(
                message="内部服务器错误",
                error_code="INTERNAL_ERROR"
            ).dict()
        )
    
    return app


# 创建应用实例
app = create_app()


# 依赖注入
def get_document_service() -> DocumentService:
    if document_service is None:
        raise HTTPException(status_code=503, detail="文档服务未初始化")
    return document_service


def get_search_service() -> SearchService:
    if search_service is None:
        raise HTTPException(status_code=503, detail="搜索服务未初始化")
    return search_service


def get_health_service() -> HealthService:
    if health_service is None:
        raise HTTPException(status_code=503, detail="健康检查服务未初始化")
    return health_service


# API 路由
@app.get("/", summary="根路径")
async def root():
    """根路径，返回 API 信息"""
    settings = get_settings()
    return {
        "name": settings.api_title,
        "version": settings.api_version,
        "description": settings.api_description,
        "timestamp": time.time()
    }


@app.get("/api/v1/health", response_model=HealthResponse, summary="健康检查")
async def health_check(service: HealthService = Depends(get_health_service)):
    """健康检查接口"""
    return await service.get_health_status()


@app.post("/api/v1/documents", summary="创建文档")
async def create_document(
    document: DocumentCreate,
    service: DocumentService = Depends(get_document_service)
):
    """创建新文档"""
    try:
        created_doc = service.create_document(document)
        return create_success_response(
            data=created_doc.dict(),
            message="文档创建成功"
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@app.get("/api/v1/documents/{doc_id}", summary="获取文档")
async def get_document(
    doc_id: int,
    service: DocumentService = Depends(get_document_service)
):
    """根据 ID 获取文档"""
    try:
        document = service.get_document(doc_id)
        return create_success_response(
            data=document.dict(),
            message="文档获取成功"
        )
    except Exception as e:
        raise HTTPException(status_code=404, detail=str(e))


@app.put("/api/v1/documents/{doc_id}", summary="更新文档")
async def update_document(
    doc_id: int,
    update_data: DocumentUpdate,
    service: DocumentService = Depends(get_document_service)
):
    """更新文档"""
    try:
        updated_doc = service.update_document(doc_id, update_data)
        return create_success_response(
            data=updated_doc.dict(),
            message="文档更新成功"
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@app.delete("/api/v1/documents/{doc_id}", summary="删除文档")
async def delete_document(
    doc_id: int,
    service: DocumentService = Depends(get_document_service)
):
    """删除文档"""
    try:
        success = service.delete_document(doc_id)
        return create_success_response(
            data={"deleted": success},
            message="文档删除成功"
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@app.get("/api/v1/documents", summary="列出文档")
async def list_documents(
    limit: int = 20,
    offset: int = 0,
    category: str = None,
    service: DocumentService = Depends(get_document_service)
):
    """列出文档"""
    try:
        documents = service.list_documents(limit, offset, category)
        return create_success_response(
            data=[doc.dict() for doc in documents],
            message="文档列表获取成功"
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@app.post("/api/v1/documents/bulk", summary="批量创建文档")
async def bulk_create_documents(
    bulk_data: BulkDocumentCreate,
    service: DocumentService = Depends(get_document_service)
):
    """批量创建文档"""
    try:
        result = service.bulk_create_documents(bulk_data)
        return create_success_response(
            data=result,
            message="批量创建完成"
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@app.post("/api/v1/search", summary="搜索文档")
async def search_documents(
    request: SearchRequest,
    service: SearchService = Depends(get_search_service)
):
    """搜索文档"""
    try:
        response = service.search(request)
        return create_success_response(
            data=response.dict(),
            message="搜索完成"
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@app.get("/api/v1/suggest", summary="搜索建议")
async def get_suggestions(
    query: str,
    limit: int = 10,
    service: SearchService = Depends(get_search_service)
):
    """获取搜索建议"""
    try:
        suggestions = service.suggest(query, limit)
        return create_success_response(
            data={"suggestions": suggestions},
            message="建议获取成功"
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@app.get("/api/v1/stats", summary="获取统计信息")
async def get_stats(service: DocumentService = Depends(get_document_service)):
    """获取文档统计信息"""
    try:
        stats = service.get_document_stats()
        return create_success_response(
            data=stats.dict(),
            message="统计信息获取成功"
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@app.get("/api/v1/vector/performance", summary="获取向量搜索性能摘要")
async def get_vector_performance(service: SearchService = Depends(get_search_service)):
    """获取向量搜索性能摘要"""
    try:
        performance = service.get_vector_performance_summary()
        return create_success_response(
            data=performance,
            message="向量搜索性能摘要获取成功"
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@app.post("/api/v1/vector/optimize", summary="优化向量搜索设置")
async def optimize_vector_settings(
    vector_count: int,
    avg_qps: float = 10.0,
    service: SearchService = Depends(get_search_service)
):
    """优化向量搜索设置"""
    try:
        query_patterns = {"avg_qps": avg_qps}
        optimization = service.optimize_vector_settings(vector_count, query_patterns)
        return create_success_response(
            data=optimization,
            message="向量搜索设置优化完成"
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


if __name__ == "__main__":
    import uvicorn
    
    settings = get_settings()
    uvicorn.run(
        "manticore_search.api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level=settings.log_level.lower()
    )
