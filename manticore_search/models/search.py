"""
搜索相关数据模型

定义搜索请求和响应的 Pydantic 模型
"""

from typing import Optional, List, Dict, Any, Union
from enum import Enum
from pydantic import BaseModel, Field, validator
from .document import DocumentWithScore


class SearchType(str, Enum):
    """搜索类型枚举"""
    FULLTEXT = "fulltext"      # 全文搜索
    VECTOR = "vector"          # 向量搜索
    HYBRID = "hybrid"          # 混合搜索


class SortOrder(str, Enum):
    """排序方向枚举"""
    ASC = "asc"
    DESC = "desc"


class SearchRequest(BaseModel):
    """搜索请求模型"""
    query: str = Field(..., description="搜索查询", max_length=1000)
    search_type: SearchType = Field(default=SearchType.FULLTEXT, description="搜索类型")
    
    # 分页参数
    limit: int = Field(default=20, description="返回结果数量", ge=1, le=100)
    offset: int = Field(default=0, description="结果偏移量", ge=0)
    
    # 过滤参数
    category: Optional[str] = Field(None, description="分类过滤", max_length=100)
    date_from: Optional[str] = Field(None, description="开始日期 (YYYY-MM-DD)")
    date_to: Optional[str] = Field(None, description="结束日期 (YYYY-MM-DD)")
    
    # 排序参数
    sort_by: Optional[str] = Field(None, description="排序字段")
    sort_order: SortOrder = Field(default=SortOrder.DESC, description="排序方向")
    
    # 高亮参数
    enable_snippet: bool = Field(default=True, description="是否启用摘要高亮")
    snippet_length: Optional[int] = Field(None, description="摘要长度", ge=50, le=500)
    snippet_around: Optional[int] = Field(None, description="摘要前后词数", ge=1, le=20)
    
    # 向量搜索参数
    query_vector: Optional[List[float]] = Field(None, description="查询向量")
    vector_weight: float = Field(default=0.5, description="向量搜索权重", ge=0.0, le=1.0)
    
    # 高级参数
    min_score: Optional[float] = Field(None, description="最小相关性分数", ge=0.0)
    boost_fields: Optional[Dict[str, float]] = Field(None, description="字段权重提升")

    # 向量搜索优化参数
    accuracy_requirement: float = Field(default=0.8, description="精度要求", ge=0.0, le=1.0)
    performance_priority: float = Field(default=0.5, description="性能优先级", ge=0.0, le=1.0)
    
    @validator('query')
    def validate_query_not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('搜索查询不能为空')
        return v.strip()
    
    @validator('query_vector')
    def validate_query_vector(cls, v, values):
        if v is not None:
            if len(v) != 128:  # 默认向量维度
                raise ValueError('查询向量维度必须为 128')
            
            # 如果提供了向量，搜索类型应该是向量或混合搜索
            search_type = values.get('search_type')
            if search_type == SearchType.FULLTEXT:
                raise ValueError('全文搜索不需要提供查询向量')
        
        elif values.get('search_type') in [SearchType.VECTOR, SearchType.HYBRID]:
            raise ValueError('向量搜索和混合搜索需要提供查询向量')
        
        return v
    
    @validator('date_from', 'date_to')
    def validate_date_format(cls, v):
        if v is not None:
            try:
                from datetime import datetime
                datetime.strptime(v, '%Y-%m-%d')
            except ValueError:
                raise ValueError('日期格式必须为 YYYY-MM-DD')
        return v


class SearchResult(BaseModel):
    """单个搜索结果模型"""
    document: DocumentWithScore = Field(..., description="文档信息")
    match_info: Optional[Dict[str, Any]] = Field(None, description="匹配信息")
    
    class Config:
        from_attributes = True


class SearchResponse(BaseModel):
    """搜索响应模型"""
    results: List[SearchResult] = Field(..., description="搜索结果列表")
    total: int = Field(..., description="总结果数")
    took: float = Field(..., description="搜索耗时(毫秒)")
    
    # 分页信息
    limit: int = Field(..., description="每页结果数")
    offset: int = Field(..., description="结果偏移量")
    has_more: bool = Field(..., description="是否有更多结果")
    
    # 搜索元信息
    search_type: SearchType = Field(..., description="搜索类型")
    query: str = Field(..., description="原始查询")
    
    # 统计信息
    max_score: Optional[float] = Field(None, description="最高相关性分数")
    min_score: Optional[float] = Field(None, description="最低相关性分数")
    avg_score: Optional[float] = Field(None, description="平均相关性分数")
    
    class Config:
        from_attributes = True


class SuggestRequest(BaseModel):
    """搜索建议请求模型"""
    query: str = Field(..., description="查询文本", max_length=100)
    limit: int = Field(default=10, description="建议数量", ge=1, le=20)
    
    @validator('query')
    def validate_query_not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('查询文本不能为空')
        return v.strip()


class SuggestResponse(BaseModel):
    """搜索建议响应模型"""
    suggestions: List[str] = Field(..., description="建议列表")
    query: str = Field(..., description="原始查询")


class SearchStats(BaseModel):
    """搜索统计信息模型"""
    total_searches: int = Field(..., description="总搜索次数")
    avg_response_time: float = Field(..., description="平均响应时间(毫秒)")
    popular_queries: List[Dict[str, Any]] = Field(..., description="热门查询")
    search_type_distribution: Dict[str, int] = Field(..., description="搜索类型分布")


class AdvancedSearchRequest(SearchRequest):
    """高级搜索请求模型"""
    # 布尔查询
    must: Optional[List[str]] = Field(None, description="必须包含的词")
    must_not: Optional[List[str]] = Field(None, description="必须不包含的词")
    should: Optional[List[str]] = Field(None, description="应该包含的词")
    
    # 范围查询
    score_range: Optional[Dict[str, float]] = Field(None, description="分数范围")
    date_range: Optional[Dict[str, str]] = Field(None, description="日期范围")
    
    # 聚合查询
    aggregations: Optional[Dict[str, Any]] = Field(None, description="聚合查询")
    
    @validator('must', 'must_not', 'should')
    def validate_term_lists(cls, v):
        if v is not None:
            if not isinstance(v, list) or len(v) == 0:
                raise ValueError('词列表不能为空')
            for term in v:
                if not term or not term.strip():
                    raise ValueError('词项不能为空')
        return v


# 工具函数
def create_simple_search_request(query: str, limit: int = 20) -> SearchRequest:
    """创建简单搜索请求"""
    return SearchRequest(
        query=query,
        search_type=SearchType.FULLTEXT,
        limit=limit
    )


def create_vector_search_request(
    query: str, 
    query_vector: List[float], 
    limit: int = 10
) -> SearchRequest:
    """创建向量搜索请求"""
    return SearchRequest(
        query=query,
        search_type=SearchType.VECTOR,
        query_vector=query_vector,
        limit=limit
    )


def create_hybrid_search_request(
    query: str,
    query_vector: List[float],
    vector_weight: float = 0.5,
    limit: int = 20
) -> SearchRequest:
    """创建混合搜索请求"""
    return SearchRequest(
        query=query,
        search_type=SearchType.HYBRID,
        query_vector=query_vector,
        vector_weight=vector_weight,
        limit=limit
    )


if __name__ == "__main__":
    # 测试搜索模型
    from manticore_search.models.document import create_sample_embedding
    
    # 测试全文搜索
    fulltext_request = create_simple_search_request("测试查询", 10)
    print("全文搜索请求:")
    print(fulltext_request.json(indent=2, ensure_ascii=False))
    
    # 测试向量搜索
    vector = create_sample_embedding("测试查询")
    vector_request = create_vector_search_request("测试查询", vector, 5)
    print("\n向量搜索请求:")
    print(vector_request.json(indent=2, ensure_ascii=False))
