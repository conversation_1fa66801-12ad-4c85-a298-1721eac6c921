"""
LLM集成服务配置管理

基于全局配置模板的LLM集成服务专用配置
"""

from pydantic import Field, validator
from typing import Optional, List, Dict
from functools import lru_cache
import sys
import os

# 添加项目根目录到路径，以便导入全局配置模板
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))
from scripts.config.config_template import BaseServiceSettings, ExternalServiceMixin, RedisMixin


class LLMIntegrationSettings(BaseServiceSettings, ExternalServiceMixin, RedisMixin):
    """LLM集成服务配置类"""
    
    # ===== 服务特定配置 =====
    api_port: int = Field(
        default=9006,
        description="LLM集成服务端口"
    )
    api_title: str = Field(
        default="LLM Integration API",
        description="LLM集成服务API标题"
    )
    api_description: str = Field(
        default="大语言模型集成和对话服务",
        description="LLM集成服务API描述"
    )
    
    # ===== 外部服务依赖 =====
    manticore_search_url: str = Field(
        default="http://localhost:9000",
        description="Manticore搜索服务URL"
    )
    embedding_service_url: str = Field(
        default="http://localhost:9001",
        description="向量化服务URL"
    )
    
    # ===== OpenAI配置 =====
    openai_api_key: str = Field(
        default="",
        description="OpenAI API密钥"
    )
    openai_base_url: str = Field(
        default="https://api.openai.com/v1",
        description="OpenAI API基础URL"
    )
    openai_default_model: str = Field(
        default="gpt-3.5-turbo",
        description="默认OpenAI模型"
    )
    openai_max_tokens: int = Field(
        default=2000,
        ge=100, le=8000,
        description="OpenAI最大token数"
    )
    openai_temperature: float = Field(
        default=0.7,
        ge=0.0, le=2.0,
        description="OpenAI温度参数"
    )
    
    # ===== Claude配置 =====
    claude_api_key: str = Field(
        default="",
        description="Claude API密钥"
    )
    claude_base_url: str = Field(
        default="https://api.anthropic.com",
        description="Claude API基础URL"
    )
    claude_default_model: str = Field(
        default="claude-3-sonnet-20240229",
        description="默认Claude模型"
    )
    claude_max_tokens: int = Field(
        default=2000,
        ge=100, le=8000,
        description="Claude最大token数"
    )
    
    # ===== RAG配置 =====
    enable_rag: bool = Field(
        default=True,
        description="是否启用RAG检索"
    )
    rag_top_k: int = Field(
        default=5,
        ge=1, le=20,
        description="RAG检索top-k数量"
    )
    rag_similarity_threshold: float = Field(
        default=0.7,
        ge=0.0, le=1.0,
        description="RAG相似度阈值"
    )
    rag_context_window: int = Field(
        default=4000,
        ge=1000, le=16000,
        description="RAG上下文窗口大小"
    )
    
    # ===== 提示工程配置 =====
    system_prompt_template: str = Field(
        default="你是一个专业的AI学习导师，基于提供的知识库内容回答用户问题。",
        description="系统提示模板"
    )
    enable_prompt_caching: bool = Field(
        default=True,
        description="是否启用提示缓存"
    )
    prompt_cache_ttl: int = Field(
        default=3600,  # 1小时
        ge=300, le=86400,
        description="提示缓存过期时间(秒)"
    )
    
    # ===== 模型路由配置 =====
    default_provider: str = Field(
        default="openai",
        description="默认模型提供商"
    )
    enable_fallback: bool = Field(
        default=True,
        description="是否启用模型降级"
    )
    fallback_providers: List[str] = Field(
        default=["openai", "claude"],
        description="降级模型提供商列表"
    )
    
    # ===== 安全配置 =====
    enable_content_filter: bool = Field(
        default=True,
        description="是否启用内容过滤"
    )
    max_conversation_length: int = Field(
        default=20,
        ge=5, le=100,
        description="最大对话轮数"
    )
    rate_limit_per_minute: int = Field(
        default=60,
        ge=10, le=1000,
        description="每分钟请求限制"
    )
    
    @validator('openai_api_key', 'claude_api_key')
    def validate_api_keys(cls, v):
        if not v:
            import warnings
            warnings.warn("API密钥未设置，对应的模型提供商将不可用", UserWarning)
        return v
    
    @validator('openai_base_url', 'claude_base_url', 'manticore_search_url', 'embedding_service_url')
    def validate_service_urls(cls, v):
        if not v.startswith(('http://', 'https://')):
            raise ValueError("服务URL必须以http://或https://开头")
        return v.rstrip('/')
    
    @validator('default_provider')
    def validate_default_provider(cls, v):
        valid_providers = ['openai', 'claude', 'local']
        if v not in valid_providers:
            raise ValueError(f'默认提供商必须是以下之一: {valid_providers}')
        return v
    
    class Config:
        env_prefix = "LLM_"
        env_file = ".env"
        case_sensitive = False


@lru_cache()
def get_settings() -> LLMIntegrationSettings:
    """获取LLM集成服务配置实例（单例模式）"""
    from scripts.config.config_template import get_service_settings
    return get_service_settings(LLMIntegrationSettings)


def create_env_example() -> str:
    """创建LLM集成服务的.env.example文件内容"""
    from scripts.config.config_template import create_env_template
    
    additional_vars = {
        # 外部服务依赖
        "MANTICORE_SEARCH_URL": "http://localhost:9000",
        "EMBEDDING_SERVICE_URL": "http://localhost:9001",
        
        # OpenAI配置
        "OPENAI_API_KEY": "your_openai_api_key_here",
        "OPENAI_BASE_URL": "https://api.openai.com/v1",
        "OPENAI_DEFAULT_MODEL": "gpt-3.5-turbo",
        "OPENAI_MAX_TOKENS": "2000",
        "OPENAI_TEMPERATURE": "0.7",
        
        # Claude配置
        "CLAUDE_API_KEY": "your_claude_api_key_here",
        "CLAUDE_BASE_URL": "https://api.anthropic.com",
        "CLAUDE_DEFAULT_MODEL": "claude-3-sonnet-20240229",
        "CLAUDE_MAX_TOKENS": "2000",
        
        # RAG配置
        "ENABLE_RAG": "true",
        "RAG_TOP_K": "5",
        "RAG_SIMILARITY_THRESHOLD": "0.7",
        "RAG_CONTEXT_WINDOW": "4000",
        
        # 提示工程配置
        "SYSTEM_PROMPT_TEMPLATE": "你是一个专业的AI学习导师，基于提供的知识库内容回答用户问题。",
        "ENABLE_PROMPT_CACHING": "true",
        "PROMPT_CACHE_TTL": "3600",
        
        # 模型路由配置
        "DEFAULT_PROVIDER": "openai",
        "ENABLE_FALLBACK": "true",
        "FALLBACK_PROVIDERS": '["openai", "claude"]',
        
        # 安全配置
        "ENABLE_CONTENT_FILTER": "true",
        "MAX_CONVERSATION_LENGTH": "20",
        "RATE_LIMIT_PER_MINUTE": "60",
        
        # Redis配置
        "REDIS_URL": "redis://localhost:6379",
        "ENABLE_REDIS": "true",
    }
    
    return create_env_template("LLM Integration Service", "LLM", additional_vars)


if __name__ == "__main__":
    # 测试配置加载
    try:
        settings = get_settings()
        print("✅ LLM集成服务配置加载成功")
        print(f"API端口: {settings.api_port}")
        print(f"默认提供商: {settings.default_provider}")
        print(f"RAG检索: {'启用' if settings.enable_rag else '禁用'}")
        print(f"上下文窗口: {settings.rag_context_window}")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
    
    # 生成.env.example文件
    env_content = create_env_example()
    with open("llm_integration/.env.example", "w", encoding="utf-8") as f:
        f.write(env_content)
    print("✅ 已生成 llm_integration/.env.example 文件")
