# AI对话引擎 (LLM Integration)

**优先级**: Priority 6 - 核心功能模块  
**状态**: 🤖 待开发  
**依赖**: embedding_service, manticore_search

## 🎯 模块职责

AI对话引擎是知深学习导师的智能核心，负责与大语言模型的集成和对话管理，提供智能的引导式学习对话能力。

### 核心功能
- **多模型支持**: OpenAI GPT, Claude, 本地模型
- **智能提示工程**: 动态构建学习导师人设和上下文
- **上下文管理**: 结合Manticore Search的智能上下文检索
- **流式响应**: 支持实时流式对话
- **模型切换**: 根据场景智能选择最适合的模型

## 🏗️ 技术架构

### 技术栈
- **Python 3.9+**: 主要开发语言
- **FastAPI**: API服务框架
- **OpenAI API**: GPT模型集成
- **Anthropic API**: Claude模型集成
- **Transformers**: 本地模型支持
- **asyncio**: 异步处理

### 架构设计
```
┌─────────────────────────────────────────────────────────────┐
│                    FastAPI Application                      │
├─────────────────────────────────────────────────────────────┤
│                      API Layer                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │    Chat     │ │   Stream    │ │   Model     │          │
│  │   Routes    │ │   Routes    │ │  Routes     │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                    Service Layer                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │    LLM      │ │   Prompt    │ │  Context    │          │
│  │  Service    │ │ Engineering │ │  Manager    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                   Model Layer                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   OpenAI    │ │   Claude    │ │   Local     │          │
│  │  Adapter    │ │  Adapter    │ │  Adapter    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                Integration Layer                           │
│  ┌─────────────┐ ┌─────────────────────────────────────────┐ │
│  │ Manticore   │ │         Embedding Service              │ │
│  │   Search    │ │        (Context Retrieval)             │ │
│  └─────────────┘ └─────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 📁 目录结构

```
llm_integration/
├── __init__.py              # 模块入口和导出
├── api/                     # API 层
│   ├── __init__.py
│   └── main.py             # FastAPI 应用和路由
├── services/               # 业务逻辑层
│   ├── __init__.py
│   ├── llm_service.py      # LLM服务
│   ├── prompt_engineering.py # 提示工程
│   ├── context_manager.py   # 上下文管理
│   └── model_router.py     # 模型路由
├── adapters/               # 模型适配器
│   ├── __init__.py
│   ├── openai_adapter.py   # OpenAI适配器
│   ├── claude_adapter.py   # Claude适配器
│   └── local_adapter.py    # 本地模型适配器
├── models/                 # 数据模型层
│   ├── __init__.py
│   ├── chat.py            # 对话模型
│   ├── prompt.py          # 提示模型
│   └── response.py        # 响应模型
├── utils/                  # 工具模块
│   ├── __init__.py
│   ├── config.py          # 配置管理
│   ├── streaming.py       # 流式处理
│   └── exceptions.py      # 异常定义
├── prompts/               # 提示模板
│   ├── system_prompts.py  # 系统提示
│   ├── learning_prompts.py # 学习提示
│   └── context_templates.py # 上下文模板
├── tests/                  # 测试模块
│   ├── __init__.py
│   ├── test_llm_service.py
│   ├── test_prompt_engineering.py
│   └── test_adapters.py
├── docs/                   # 文档目录
│   └── api-documentation.md
├── requirements.txt        # 依赖包列表
├── test_module.py         # 模块测试脚本
└── README.md              # 模块说明 (本文件)
```

## 🚀 快速开始

### 环境要求
- Python 3.9+
- OpenAI API Key (可选)
- Anthropic API Key (可选)
- manticore_search (上下文检索)
- embedding_service (向量化)

### 1. 安装依赖
```bash
cd llm_integration
source ../.venv/bin/activate
pip install -r requirements.txt
```

### 2. 配置API密钥
```bash
# 编辑配置文件
# OPENAI_API_KEY=your_openai_key_here
# ANTHROPIC_API_KEY=your_claude_key_here
# DEFAULT_MODEL=openai  # openai, claude, local
```

### 3. 启动服务
```bash
uvicorn api.main:app --reload --port 8006
```

## 💻 使用示例

### 基础对话
```python
from llm_integration import LLMService, ChatRequest

llm_service = LLMService()

# 创建对话请求
chat_request = ChatRequest(
    message="请解释Python的装饰器概念",
    context={
        "topic": "Python编程",
        "user_level": "初学者",
        "learning_goal": "理解装饰器的基本概念和用法"
    }
)

# 获取AI回复
response = llm_service.generate_response(chat_request)
print(response.content)
```

### 流式对话
```python
# 流式响应
async def stream_chat():
    async for chunk in llm_service.stream_response(chat_request):
        print(chunk.content, end='', flush=True)
```

### 智能上下文构建
```python
from llm_integration import ContextManager

context_manager = ContextManager()

# 构建学习上下文
context = context_manager.build_learning_context(
    current_message="什么是神经网络？",
    topic_id=1,
    conversation_history=recent_messages,
    user_profile=user_profile
)

print(context.system_prompt)
print(context.context_documents)
```

## 🔗 API接口设计

### 对话接口
```python
# 单轮对话
POST /api/v1/chat
{
    "message": "用户消息",
    "context": {
        "topic_id": 1,
        "conversation_id": 123,
        "user_level": "初学者"
    },
    "model": "openai"  # 可选
}

# 流式对话
POST /api/v1/chat/stream
# 返回 Server-Sent Events 流

# 多轮对话
POST /api/v1/chat/conversation
{
    "conversation_id": 123,
    "message": "用户消息"
}
```

### 模型管理接口
```python
# 获取可用模型
GET /api/v1/models

# 切换模型
POST /api/v1/models/switch
{
    "model": "claude",
    "conversation_id": 123
}

# 模型状态
GET /api/v1/models/status
```

## 🧠 智能提示工程

### 学习导师人设
```python
LEARNING_TUTOR_SYSTEM_PROMPT = """
你是一位经验丰富的学习导师，专门帮助用户深度理解和内化知识。

核心原则:
1. 不直接给答案，而是通过提问引导用户思考
2. 使用类比和比喻帮助理解复杂概念
3. 鼓励用户用自己的话解释概念
4. 根据用户的回答调整教学策略
5. 保持耐心和鼓励的态度

教学风格:
- 苏格拉底式提问法
- 循序渐进的知识构建
- 及时的正面反馈
- 个性化的学习路径
"""
```

### 动态上下文构建
```python
def build_dynamic_context(self, request: ChatRequest) -> str:
    """动态构建上下文"""
    
    # 1. 基础人设
    context = self.get_system_prompt(request.context.get("user_level"))
    
    # 2. 主题相关知识
    if request.context.get("topic_id"):
        topic_docs = self.retrieve_topic_documents(
            request.context["topic_id"], 
            request.message
        )
        context += f"\n\n相关知识:\n{topic_docs}"
    
    # 3. 对话历史
    if request.context.get("conversation_id"):
        history = self.get_conversation_summary(
            request.context["conversation_id"]
        )
        context += f"\n\n对话历史:\n{history}"
    
    # 4. 学习目标
    if request.context.get("learning_goal"):
        context += f"\n\n学习目标: {request.context['learning_goal']}"
    
    return context
```

## 🔄 模型适配器设计

### OpenAI适配器
```python
class OpenAIAdapter:
    def __init__(self, api_key: str):
        self.client = OpenAI(api_key=api_key)
    
    async def generate_response(self, prompt: str, **kwargs) -> str:
        response = await self.client.chat.completions.create(
            model="gpt-4",
            messages=[{"role": "user", "content": prompt}],
            stream=kwargs.get("stream", False),
            temperature=kwargs.get("temperature", 0.7)
        )
        return response.choices[0].message.content
    
    async def stream_response(self, prompt: str, **kwargs):
        stream = await self.client.chat.completions.create(
            model="gpt-4",
            messages=[{"role": "user", "content": prompt}],
            stream=True,
            temperature=kwargs.get("temperature", 0.7)
        )
        
        async for chunk in stream:
            if chunk.choices[0].delta.content:
                yield chunk.choices[0].delta.content
```

### Claude适配器
```python
class ClaudeAdapter:
    def __init__(self, api_key: str):
        self.client = anthropic.Anthropic(api_key=api_key)
    
    async def generate_response(self, prompt: str, **kwargs) -> str:
        response = await self.client.messages.create(
            model="claude-3-sonnet-20240229",
            max_tokens=kwargs.get("max_tokens", 1000),
            messages=[{"role": "user", "content": prompt}]
        )
        return response.content[0].text
```

## 🔗 与其他模块集成

### 与Manticore Search集成
```python
def retrieve_relevant_context(self, query: str, topic_id: int) -> str:
    """检索相关上下文"""
    from manticore_search import SearchService, create_hybrid_search_request
    
    # 向量化查询
    query_vector = embedding_service.embed_text(query)
    
    # 混合搜索
    search_request = create_hybrid_search_request(
        query=query,
        query_vector=query_vector.embedding,
        limit=3,
        filters={"topic_id": topic_id}
    )
    
    results = search_service.search(search_request)
    return self.format_search_results(results)
```

### 与Embedding Service集成
```python
def enhance_with_semantic_search(self, message: str, context: Dict) -> str:
    """使用语义搜索增强上下文"""
    
    # 生成消息向量
    message_vector = embedding_service.embed_text(message)
    
    # 搜索相似的历史对话
    similar_conversations = self.search_similar_conversations(
        message_vector.embedding,
        context.get("user_id")
    )
    
    return self.format_similar_context(similar_conversations)
```

## 🧪 测试策略

### 单元测试
- LLM适配器测试
- 提示工程测试
- 上下文构建测试
- 流式响应测试

### 集成测试
- 多模型切换测试
- 上下文检索集成测试
- 实时对话测试

### 性能测试
- 响应时间测试
- 并发处理测试
- 流式传输性能测试

## 📊 性能指标

### 目标性能
- **对话响应**: <2s (非流式)
- **流式首字**: <500ms
- **上下文构建**: <300ms
- **并发对话**: 100+

## 🔧 配置说明

### 环境变量
```bash
# API密钥
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_claude_key_here

# 模型配置
DEFAULT_MODEL=openai
OPENAI_MODEL=gpt-4
CLAUDE_MODEL=claude-3-sonnet-20240229
LOCAL_MODEL_PATH=/path/to/local/model

# 服务依赖
MANTICORE_HOST=localhost
MANTICORE_PORT=9306
EMBEDDING_SERVICE_URL=http://localhost:8001

# 对话配置
MAX_CONTEXT_LENGTH=4000
DEFAULT_TEMPERATURE=0.7
MAX_TOKENS=1000
STREAM_CHUNK_SIZE=64

# API配置
API_HOST=0.0.0.0
API_PORT=8006
```

---

**模块版本**: 1.0.0  
**创建日期**: 2025-08-13  
**维护者**: Winston (Architect)
