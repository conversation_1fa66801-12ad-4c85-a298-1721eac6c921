# LLM Integration Service Configuration

# ===== API服务配置 =====
LLM_API_HOST=0.0.0.0
LLM_API_PORT=9006
LLM_API_TITLE="LLM Integration API"
LLM_API_DESCRIPTION="大语言模型集成和对话服务"
LLM_API_VERSION=1.0.0
LLM_API_PREFIX=/api/v1

# ===== 日志配置 =====
LLM_LOG_LEVEL=INFO
LLM_LOG_FORMAT=json
LLM_ENABLE_REQUEST_LOGGING=true

# ===== 健康检查配置 =====
LLM_HEALTH_CHECK_TIMEOUT=5
LLM_ENABLE_HEALTH_CHECK=true

# ===== 安全配置 =====
LLM_ENABLE_CORS=true
LLM_CORS_ORIGINS=["*"]

# ===== 环境配置 =====
LLM_ENVIRONMENT=development
LLM_DEBUG=false

# ===== 外部服务配置 =====
LLM_REQUEST_TIMEOUT=30
LLM_MAX_RETRIES=3
LLM_RETRY_DELAY=1.0

# ===== 服务特定配置 =====
# 外部服务依赖
LLM_MANTICORE_SEARCH_URL=http://localhost:9000
LLM_EMBEDDING_SERVICE_URL=http://localhost:9001

# OpenAI配置
LLM_OPENAI_API_KEY=your_openai_api_key_here
LLM_OPENAI_BASE_URL=https://api.openai.com/v1
LLM_OPENAI_DEFAULT_MODEL=gpt-3.5-turbo
LLM_OPENAI_MAX_TOKENS=2000
LLM_OPENAI_TEMPERATURE=0.7

# Claude配置
LLM_CLAUDE_API_KEY=your_claude_api_key_here
LLM_CLAUDE_BASE_URL=https://api.anthropic.com
LLM_CLAUDE_DEFAULT_MODEL=claude-3-sonnet-20240229
LLM_CLAUDE_MAX_TOKENS=2000

# RAG配置
LLM_ENABLE_RAG=true
LLM_RAG_TOP_K=5
LLM_RAG_SIMILARITY_THRESHOLD=0.7
LLM_RAG_CONTEXT_WINDOW=4000

# 提示工程配置
LLM_SYSTEM_PROMPT_TEMPLATE=你是一个专业的AI学习导师，基于提供的知识库内容回答用户问题。
LLM_ENABLE_PROMPT_CACHING=true
LLM_PROMPT_CACHE_TTL=3600

# 模型路由配置
LLM_DEFAULT_PROVIDER=openai
LLM_ENABLE_FALLBACK=true
LLM_FALLBACK_PROVIDERS=["openai", "claude"]

# 安全配置
LLM_ENABLE_CONTENT_FILTER=true
LLM_MAX_CONVERSATION_LENGTH=20
LLM_RATE_LIMIT_PER_MINUTE=60

# Redis配置
LLM_REDIS_URL=redis://localhost:6379
LLM_ENABLE_REDIS=true
