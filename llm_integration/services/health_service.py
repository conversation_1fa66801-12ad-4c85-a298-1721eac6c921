"""
LLM集成服务健康检查

基于统一健康检查框架的LLM集成服务健康检查实现
"""

import sys
import os
from typing import Dict

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))
from scripts.health.health_framework import HealthChecker, ServiceHealth, HealthStatus, DatabaseHealthChecker
from llm_integration.utils.config import get_settings


class LlmIntegration<PERSON>ealthChecker(HealthChecker):
    """LLM集成服务健康检查器"""
    
    def __init__(self):
        super().__init__("llm_integration", "1.0.0")
        self.settings = get_settings()
    
    async def check_dependencies(self) -> Dict[str, ServiceHealth]:
        """检查依赖服务健康状态"""
        dependencies = {}
        # 检查Redis连接
        if (hasattr(self.settings, 'enable_redis') and self.settings.enable_redis and 
            hasattr(self.settings, 'redis_url') and self.settings.redis_url):
            redis_health = await DatabaseHealthChecker.check_redis(
                self.settings.redis_url,
                timeout=self.settings.health_check_timeout
            )
            dependencies["redis"] = redis_health
        # 检查manticore_search服务
        if hasattr(self.settings, 'manticore_search_url') and self.settings.manticore_search_url:
            manticore_search_health = await self.check_service_url(
                "manticore_search",
                self.settings.manticore_search_url,
                timeout=self.settings.health_check_timeout
            )
            dependencies["manticore_search"] = manticore_search_health
        # 检查embedding_service服务
        if hasattr(self.settings, 'embedding_service_url') and self.settings.embedding_service_url:
            embedding_service_health = await self.check_service_url(
                "embedding_service",
                self.settings.embedding_service_url,
                timeout=self.settings.health_check_timeout
            )
            dependencies["embedding_service"] = embedding_service_health
        
        return dependencies
    
    async def check_self(self) -> ServiceHealth:
        """检查自身服务健康状态"""
        try:
            # 检查服务基本功能
            uptime = self.get_uptime()
            
            details = {
                "default_provider": getattr(self.settings, 'default_provider', 'openai'),
                "rag_enabled": getattr(self.settings, 'enable_rag', True),
                "fallback_enabled": getattr(self.settings, 'enable_fallback', True),
            }
            
            # 确定状态
            status = HealthStatus.HEALTHY
            message = "LLM集成服务 is operational"
            
            return ServiceHealth(
                name=self.service_name,
                status=status,
                response_time_ms=0.0,
                message=message,
                details=details,
                timestamp=self.get_current_timestamp()
            )
            
        except Exception as e:
            return ServiceHealth(
                name=self.service_name,
                status=HealthStatus.ERROR,
                response_time_ms=0.0,
                message=f"Self check failed: {str(e)}",
                timestamp=self.get_current_timestamp()
            )
    
    def get_uptime(self) -> float:
        """获取服务运行时间"""
        import time
        return time.time() - self.start_time
    
    def get_current_timestamp(self) -> float:
        """获取当前时间戳"""
        import time
        return time.time()


# 全局健康检查器实例
_health_checker = None

def get_health_checker() -> LlmIntegrationHealthChecker:
    """获取健康检查器实例（单例模式）"""
    global _health_checker
    if _health_checker is None:
        _health_checker = LlmIntegrationHealthChecker()
    return _health_checker


async def get_health_status():
    """获取健康状态（FastAPI兼容）"""
    checker = get_health_checker()
    return await checker.get_health_status()


async def get_ready_status():
    """获取就绪状态（Kubernetes就绪探针）"""
    checker = get_health_checker()
    health = await checker.get_health_status()
    
    # 就绪检查更严格，关键依赖都必须健康
    if health.status in [HealthStatus.HEALTHY, HealthStatus.WARNING]:
        return {
            "status": "ready",
            "service": "llm_integration",
            "timestamp": health.timestamp
        }
    else:
        return {
            "status": "not_ready",
            "service": "llm_integration",
            "message": health.message,
            "timestamp": health.timestamp
        }


if __name__ == "__main__":
    import asyncio
    
    async def test_health_check():
        """测试健康检查功能"""
        print("🧪 测试LLM集成服务健康检查...")
        
        checker = get_health_checker()
        health = await checker.get_health_status()
        
        print(f"整体状态: {health.status}")
        print(f"服务名称: {health.service_name}")
        print(f"版本: {health.version}")
        print(f"运行时间: {health.uptime_seconds:.2f}秒")
        print(f"消息: {health.message}")
        
        print("\n服务检查结果:")
        for name, service in health.services.items():
            print(f"  {name}: {service.status} ({service.response_time_ms:.2f}ms)")
            if service.message:
                print(f"    消息: {service.message}")
        
        print(f"\n系统状态:")
        print(f"  CPU: {health.system.cpu_percent:.1f}%")
        print(f"  内存: {health.system.memory_percent:.1f}%")
        print(f"  磁盘: {health.system.disk_percent:.1f}%")
        print(f"  系统状态: {health.system.status}")
        
        # 测试就绪状态
        ready = await get_ready_status()
        print(f"\n就绪状态: {ready['status']}")
    
    asyncio.run(test_health_check())