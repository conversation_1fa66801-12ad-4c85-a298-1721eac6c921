# API Gateway Configuration

# ===== API服务配置 =====
GATEWAY_API_HOST=0.0.0.0
GATEWAY_API_PORT=9003
GATEWAY_API_TITLE="API Gateway"
GATEWAY_API_DESCRIPTION="统一API网关服务"
GATEWAY_API_VERSION=1.0.0
GATEWAY_API_PREFIX=/api/v1

# ===== 日志配置 =====
GATEWAY_LOG_LEVEL=INFO
GATEWAY_LOG_FORMAT=json
GATEWAY_ENABLE_REQUEST_LOGGING=true

# ===== 健康检查配置 =====
GATEWAY_HEALTH_CHECK_TIMEOUT=5
GATEWAY_ENABLE_HEALTH_CHECK=true

# ===== 安全配置 =====
GATEWAY_ENABLE_CORS=true
GATEWAY_CORS_ORIGINS=["*"]

# ===== 环境配置 =====
GATEWAY_ENVIRONMENT=development
GATEWAY_DEBUG=false

# ===== 外部服务配置 =====
GATEWAY_REQUEST_TIMEOUT=30
GATEWAY_MAX_RETRIES=3
GATEWAY_RETRY_DELAY=1.0

# ===== 服务特定配置 =====
# 后端服务配置
GATEWAY_USER_SERVICE_URL=http://localhost:9002
GATEWAY_TOPIC_SERVICE_URL=http://localhost:9004
GATEWAY_DOCUMENT_SERVICE_URL=http://localhost:9005
GATEWAY_EMBEDDING_SERVICE_URL=http://localhost:9001
GATEWAY_LLM_INTEGRATION_URL=http://localhost:9006
GATEWAY_CONVERSATION_SERVICE_URL=http://localhost:9007
GATEWAY_SUMMARY_SERVICE_URL=http://localhost:9008
GATEWAY_MANTICORE_SEARCH_URL=http://localhost:9000

# 代理配置
GATEWAY_PROXY_TIMEOUT=30
GATEWAY_MAX_CONNECTIONS=100
GATEWAY_CONNECTION_POOL_SIZE=20

# 路由配置
GATEWAY_ENABLE_ROUTE_LOGGING=true

# 认证配置 (MVP阶段关闭)
GATEWAY_ENABLE_AUTHENTICATION=false
GATEWAY_JWT_SECRET_KEY=

# 限流配置 (MVP阶段关闭)
GATEWAY_ENABLE_RATE_LIMITING=false
GATEWAY_RATE_LIMIT_REQUESTS=100
GATEWAY_RATE_LIMIT_WINDOW=60

# 健康检查配置
GATEWAY_ENABLE_BACKEND_HEALTH_CHECK=true
GATEWAY_HEALTH_CHECK_INTERVAL=30
GATEWAY_UNHEALTHY_THRESHOLD=3

# 缓存配置 (MVP阶段关闭)
GATEWAY_ENABLE_RESPONSE_CACHE=false
GATEWAY_CACHE_TTL=300
