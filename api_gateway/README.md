# API网关 (API Gateway) - 简化版

**优先级**: Priority 3 - 基础设施模块  
**状态**: 🚪 待开发 (简化版)  
**依赖**: user_service, topic_service, document_service

## 🎯 模块职责 (简化版)

API网关的简化版本，专注于提供基础的请求路由和转发功能，作为前端和后端服务之间的"传话筒"。

### 核心功能 (简化版)
- **请求路由**: 简单的URL路径映射和转发
- **服务代理**: 将前端请求转发到对应的后端服务
- **统一入口**: 为前端提供单一的API访问点
- **基础CORS**: 支持跨域请求

### 简化设计原则
- **无认证**: 不做复杂的JWT认证，直接透传请求
- **无限流**: 不做API限流和熔断，专注于路由转发
- **无缓存**: 不做响应缓存，直接返回后端响应
- **快速实现**: 最小化功能，快速让系统跑起来

## 🏗️ 技术架构

### 技术栈 (简化版)
- **Python 3.9+**: 主要开发语言
- **FastAPI**: API服务框架
- **httpx**: HTTP客户端 (用于转发请求)
- **Pydantic**: 数据验证

### 架构设计 (简化版)
```
┌─────────────────────────────────────────────────────────────┐
│                    FastAPI Application                      │
├─────────────────────────────────────────────────────────────┤
│                      API Layer                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   Topics    │ │ Documents   │ │   Users     │          │
│  │   Proxy     │ │   Proxy     │ │   Proxy     │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                    Proxy Layer                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              HTTP Client (httpx)                       │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                  Target Services                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │    Topic    │ │  Document   │ │    User     │          │
│  │  Service    │ │  Service    │ │  Service    │          │
│  │   :8004     │ │   :8005     │ │   :8002     │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

## 📁 目录结构

```
api_gateway/
├── __init__.py              # 模块入口和导出
├── api/                     # API 层
│   ├── __init__.py
│   └── main.py             # FastAPI 应用和路由
├── services/               # 业务逻辑层
│   ├── __init__.py
│   ├── proxy_service.py    # 代理转发服务
│   └── health_service.py   # 健康检查服务
├── models/                 # 数据模型层
│   ├── __init__.py
│   ├── proxy.py           # 代理模型
│   └── response.py        # 响应模型
├── utils/                  # 工具模块
│   ├── __init__.py
│   ├── config.py          # 配置管理
│   └── exceptions.py      # 异常定义
├── tests/                  # 测试模块
│   ├── __init__.py
│   ├── test_proxy_service.py
│   └── test_routes.py
├── docs/                   # 文档目录
│   └── api-documentation.md
├── requirements.txt        # 依赖包列表
├── test_module.py         # 模块测试脚本
└── README.md              # 模块说明 (本文件)
```

## 🚀 快速开始

### 环境要求
- Python 3.9+
- user_service (运行在8002端口)
- topic_service (运行在8004端口)
- document_service (运行在8005端口)

### 1. 安装依赖
```bash
cd api_gateway
source ../.venv/bin/activate
pip install -r requirements.txt
```

### 2. 配置服务地址
```bash
# 编辑配置文件
# USER_SERVICE_URL=http://localhost:8002
# TOPIC_SERVICE_URL=http://localhost:8004
# DOCUMENT_SERVICE_URL=http://localhost:8005
```

### 3. 启动服务
```bash
uvicorn api.main:app --reload --port 8003
```

## 💻 使用示例

### 路由转发示例
```python
# 前端请求
GET http://localhost:8003/api/v1/topics

# 网关自动转发到
GET http://localhost:8004/api/v1/topics

# 前端请求
POST http://localhost:8003/api/v1/documents
Content-Type: multipart/form-data

# 网关自动转发到
POST http://localhost:8005/api/v1/documents
Content-Type: multipart/form-data
```

## 🔗 路由映射设计

### 简化路由表
```python
ROUTE_MAPPING = {
    # 用户相关路由
    "/api/v1/users": "http://localhost:8002",
    
    # 主题相关路由
    "/api/v1/topics": "http://localhost:8004",
    
    # 文档相关路由
    "/api/v1/documents": "http://localhost:8005",
    
    # 健康检查
    "/api/v1/health": "local"  # 本地处理
}
```

### 代理转发逻辑
```python
class ProxyService:
    def __init__(self):
        self.client = httpx.AsyncClient()
        self.route_mapping = ROUTE_MAPPING
    
    async def forward_request(self, path: str, method: str, **kwargs):
        """转发请求到目标服务"""
        
        # 查找目标服务
        target_service = self.find_target_service(path)
        if not target_service:
            raise ServiceNotFoundError(f"No service found for path: {path}")
        
        # 构建目标URL
        target_url = f"{target_service}{path}"
        
        # 转发请求
        response = await self.client.request(
            method=method,
            url=target_url,
            **kwargs
        )
        
        return response
    
    def find_target_service(self, path: str) -> str:
        """根据路径查找目标服务"""
        for route_prefix, service_url in self.route_mapping.items():
            if path.startswith(route_prefix):
                return service_url
        return None
```

## 🔗 API接口设计

### 代理接口 (透传所有HTTP方法)
```python
# 主题相关 (转发到topic_service)
GET    /api/v1/topics           -> topic_service:8004/api/v1/topics
POST   /api/v1/topics           -> topic_service:8004/api/v1/topics
GET    /api/v1/topics/{id}      -> topic_service:8004/api/v1/topics/{id}
PUT    /api/v1/topics/{id}      -> topic_service:8004/api/v1/topics/{id}
DELETE /api/v1/topics/{id}      -> topic_service:8004/api/v1/topics/{id}

# 文档相关 (转发到document_service)
GET    /api/v1/documents        -> document_service:8005/api/v1/documents
POST   /api/v1/documents        -> document_service:8005/api/v1/documents
GET    /api/v1/documents/{id}   -> document_service:8005/api/v1/documents/{id}
PUT    /api/v1/documents/{id}   -> document_service:8005/api/v1/documents/{id}
DELETE /api/v1/documents/{id}   -> document_service:8005/api/v1/documents/{id}

# 用户相关 (转发到user_service)
GET    /api/v1/users/me         -> user_service:8002/api/v1/users/me

# 健康检查 (本地处理)
GET    /api/v1/health           -> 返回网关和所有后端服务的健康状态
```

### 健康检查接口
```python
GET /api/v1/health
{
    "status": "healthy",
    "services": {
        "user_service": "healthy",
        "topic_service": "healthy", 
        "document_service": "healthy"
    },
    "timestamp": "2025-08-13T10:00:00Z"
}
```

## 🧪 测试策略

### 单元测试
- 路由映射测试
- 代理转发测试
- 错误处理测试
- 健康检查测试

### 集成测试
- 端到端请求转发测试
- 多服务集成测试
- 错误传播测试

### 性能测试
- 转发延迟测试
- 并发请求测试
- 服务可用性测试

## 📊 性能指标

### 目标性能
- **转发延迟**: <10ms (网关本身的开销)
- **并发请求**: 1000+ (透传能力)
- **可用性**: 99.9% (依赖后端服务)

## 🔧 配置说明

### 环境变量
```bash
# 后端服务地址
USER_SERVICE_URL=http://localhost:8002
TOPIC_SERVICE_URL=http://localhost:8004
DOCUMENT_SERVICE_URL=http://localhost:8005
EMBEDDING_SERVICE_URL=http://localhost:8001

# API配置
API_HOST=0.0.0.0
API_PORT=8003

# 代理配置
PROXY_TIMEOUT=30
MAX_CONNECTIONS=100
ENABLE_CORS=true

# 健康检查配置
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=5
```

## 🚧 开发计划

### Phase 1: 基础代理 (Week 1)
- [x] 项目结构搭建
- [ ] 基础路由转发功能
- [ ] 健康检查接口
- [ ] CORS支持

### Phase 2: 完善功能 (后续)
- [ ] 错误处理优化
- [ ] 日志记录
- [ ] 监控指标
- [ ] 性能优化

---

**模块版本**: 1.0.0 (简化版)  
**创建日期**: 2025-08-13  
**维护者**: Winston (Architect)
