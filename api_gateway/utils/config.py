"""
API网关服务配置管理

基于全局配置模板的API网关专用配置
"""

from pydantic import Field, validator
from typing import Optional, Dict, List
from functools import lru_cache
import sys
import os

# 添加项目根目录到路径，以便导入全局配置模板
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))
from scripts.config.config_template import BaseServiceSettings, ExternalServiceMixin


class APIGatewaySettings(BaseServiceSettings, ExternalServiceMixin):
    """API网关服务配置类"""
    
    # ===== 服务特定配置 =====
    api_port: int = Field(
        default=9003,
        description="API网关端口"
    )
    api_title: str = Field(
        default="API Gateway",
        description="API网关标题"
    )
    api_description: str = Field(
        default="统一API网关服务",
        description="API网关描述"
    )
    
    # ===== 后端服务配置 =====
    user_service_url: str = Field(
        default="http://localhost:9002",
        description="用户服务URL"
    )
    topic_service_url: str = Field(
        default="http://localhost:9004",
        description="主题服务URL"
    )
    document_service_url: str = Field(
        default="http://localhost:9005",
        description="文档服务URL"
    )
    embedding_service_url: str = Field(
        default="http://localhost:9001",
        description="向量化服务URL"
    )
    llm_integration_url: str = Field(
        default="http://localhost:9006",
        description="LLM集成服务URL"
    )
    conversation_service_url: str = Field(
        default="http://localhost:9007",
        description="对话服务URL"
    )
    summary_service_url: str = Field(
        default="http://localhost:9008",
        description="摘要服务URL"
    )
    manticore_search_url: str = Field(
        default="http://localhost:9000",
        description="Manticore搜索服务URL"
    )
    
    # ===== 代理配置 =====
    proxy_timeout: int = Field(
        default=30,
        ge=5, le=300,
        description="代理请求超时时间(秒)"
    )
    max_connections: int = Field(
        default=100,
        ge=10, le=1000,
        description="最大连接数"
    )
    connection_pool_size: int = Field(
        default=20,
        ge=5, le=100,
        description="连接池大小"
    )
    
    # ===== 路由配置 =====
    enable_route_logging: bool = Field(
        default=True,
        description="是否启用路由日志"
    )
    route_prefix_mapping: Dict[str, str] = Field(
        default={
            "/api/v1/users": "user_service_url",
            "/api/v1/auth": "user_service_url",
            "/api/v1/topics": "topic_service_url",
            "/api/v1/documents": "document_service_url",
            "/api/v1/embeddings": "embedding_service_url",
            "/api/v1/chat": "llm_integration_url",
            "/api/v1/conversations": "conversation_service_url",
            "/api/v1/summaries": "summary_service_url",
            "/api/v1/search": "manticore_search_url",
        },
        description="路由前缀映射"
    )
    
    # ===== 认证配置 =====
    enable_authentication: bool = Field(
        default=False,  # MVP阶段暂时关闭
        description="是否启用认证"
    )
    jwt_secret_key: Optional[str] = Field(
        default=None,
        description="JWT验证密钥"
    )
    auth_excluded_paths: List[str] = Field(
        default=[
            "/api/v1/health",
            "/api/v1/ready",
            "/docs",
            "/openapi.json",
            "/api/v1/auth/login",
            "/api/v1/auth/register",
        ],
        description="认证排除路径"
    )
    
    # ===== 限流配置 =====
    enable_rate_limiting: bool = Field(
        default=False,  # MVP阶段暂时关闭
        description="是否启用限流"
    )
    rate_limit_requests: int = Field(
        default=100,
        ge=10, le=10000,
        description="每分钟请求限制"
    )
    rate_limit_window: int = Field(
        default=60,
        ge=10, le=3600,
        description="限流时间窗口(秒)"
    )
    
    # ===== 健康检查配置 =====
    enable_backend_health_check: bool = Field(
        default=True,
        description="是否启用后端服务健康检查"
    )
    health_check_interval: int = Field(
        default=30,
        ge=10, le=300,
        description="健康检查间隔(秒)"
    )
    unhealthy_threshold: int = Field(
        default=3,
        ge=1, le=10,
        description="不健康阈值"
    )
    
    # ===== 缓存配置 =====
    enable_response_cache: bool = Field(
        default=False,  # MVP阶段暂时关闭
        description="是否启用响应缓存"
    )
    cache_ttl: int = Field(
        default=300,  # 5分钟
        ge=60, le=3600,
        description="缓存过期时间(秒)"
    )
    
    @validator('user_service_url', 'topic_service_url', 'document_service_url', 
              'embedding_service_url', 'llm_integration_url', 'conversation_service_url',
              'summary_service_url', 'manticore_search_url')
    def validate_service_urls(cls, v):
        if not v.startswith(('http://', 'https://')):
            raise ValueError("服务URL必须以http://或https://开头")
        return v.rstrip('/')
    
    def get_service_url(self, service_name: str) -> str:
        """根据服务名称获取服务URL"""
        return getattr(self, service_name)
    
    class Config:
        env_prefix = "GATEWAY_"
        env_file = ".env"
        case_sensitive = False


@lru_cache()
def get_settings() -> APIGatewaySettings:
    """获取API网关配置实例（单例模式）"""
    from scripts.config.config_template import get_service_settings
    return get_service_settings(APIGatewaySettings)


def create_env_example() -> str:
    """创建API网关的.env.example文件内容"""
    from scripts.config.config_template import create_env_template
    
    additional_vars = {
        # 后端服务配置
        "USER_SERVICE_URL": "http://localhost:8002",
        "TOPIC_SERVICE_URL": "http://localhost:8004",
        "DOCUMENT_SERVICE_URL": "http://localhost:8005",
        "EMBEDDING_SERVICE_URL": "http://localhost:8001",
        "LLM_INTEGRATION_URL": "http://localhost:8006",
        "CONVERSATION_SERVICE_URL": "http://localhost:8007",
        "SUMMARY_SERVICE_URL": "http://localhost:8008",
        "MANTICORE_SEARCH_URL": "http://localhost:8000",
        
        # 代理配置
        "PROXY_TIMEOUT": "30",
        "MAX_CONNECTIONS": "100",
        "CONNECTION_POOL_SIZE": "20",
        
        # 路由配置
        "ENABLE_ROUTE_LOGGING": "true",
        
        # 认证配置 (MVP阶段关闭)
        "ENABLE_AUTHENTICATION": "false",
        "JWT_SECRET_KEY": "",
        
        # 限流配置 (MVP阶段关闭)
        "ENABLE_RATE_LIMITING": "false",
        "RATE_LIMIT_REQUESTS": "100",
        "RATE_LIMIT_WINDOW": "60",
        
        # 健康检查配置
        "ENABLE_BACKEND_HEALTH_CHECK": "true",
        "HEALTH_CHECK_INTERVAL": "30",
        "UNHEALTHY_THRESHOLD": "3",
        
        # 缓存配置 (MVP阶段关闭)
        "ENABLE_RESPONSE_CACHE": "false",
        "CACHE_TTL": "300",
    }
    
    return create_env_template("API Gateway", "GATEWAY", additional_vars)


if __name__ == "__main__":
    # 测试配置加载
    try:
        settings = get_settings()
        print("✅ API网关配置加载成功")
        print(f"API端口: {settings.api_port}")
        print(f"用户服务URL: {settings.user_service_url}")
        print(f"代理超时: {settings.proxy_timeout}秒")
        print(f"路由映射: {len(settings.route_prefix_mapping)}个路由")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
    
    # 生成.env.example文件
    env_content = create_env_example()
    with open("api_gateway/.env.example", "w", encoding="utf-8") as f:
        f.write(env_content)
    print("✅ 已生成 api_gateway/.env.example 文件")
