#!/usr/bin/env python3
"""
批量生成健康检查服务脚本

为所有服务生成标准化的健康检查实现
"""

import os
from pathlib import Path
from typing import Dict, List


def generate_health_service(service_name: str, service_config: Dict) -> str:
    """生成健康检查服务代码"""
    
    # 服务名称转换
    class_name = ''.join(word.capitalize() for word in service_name.split('_'))
    
    # 依赖服务检查代码
    dependency_checks = []
    
    if service_config.get('has_database'):
        dependency_checks.append("""
        # 检查数据库连接
        if hasattr(self.settings, 'database_url') and self.settings.database_url:
            db_health = await DatabaseHealthChecker.check_postgresql(
                self.settings.database_url,
                timeout=self.settings.health_check_timeout
            )
            dependencies["database"] = db_health""")
    
    if service_config.get('has_redis'):
        dependency_checks.append("""
        # 检查Redis连接
        if (hasattr(self.settings, 'enable_redis') and self.settings.enable_redis and 
            hasattr(self.settings, 'redis_url') and self.settings.redis_url):
            redis_health = await DatabaseHealthChecker.check_redis(
                self.settings.redis_url,
                timeout=self.settings.health_check_timeout
            )
            dependencies["redis"] = redis_health""")
    
    # 外部服务依赖检查
    for dep_service in service_config.get('dependencies', []):
        dependency_checks.append(f"""
        # 检查{dep_service}服务
        if hasattr(self.settings, '{dep_service}_url') and self.settings.{dep_service}_url:
            {dep_service}_health = await self.check_service_url(
                "{dep_service}",
                self.settings.{dep_service}_url,
                timeout=self.settings.health_check_timeout
            )
            dependencies["{dep_service}"] = {dep_service}_health""")
    
    dependency_checks_code = ''.join(dependency_checks) if dependency_checks else """
        # 此服务无外部依赖
        pass"""
    
    # 自检代码
    self_check_details = service_config.get('self_check_details', [])
    details_code = []
    
    for detail in self_check_details:
        details_code.append(f'                "{detail["key"]}": getattr(self.settings, \'{detail["setting"]}\', {detail["default"]}),')
    
    details_code_str = '\n'.join(details_code) if details_code else '                "uptime_seconds": uptime,'
    
    template = f'''"""
{service_config['description']}健康检查

基于统一健康检查框架的{service_config['description']}健康检查实现
"""

import sys
import os
from typing import Dict

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))
from scripts.health.health_framework import HealthChecker, ServiceHealth, HealthStatus, DatabaseHealthChecker
from {service_name}.utils.config import get_settings


class {class_name}HealthChecker(HealthChecker):
    """{service_config['description']}健康检查器"""
    
    def __init__(self):
        super().__init__("{service_name}", "1.0.0")
        self.settings = get_settings()
    
    async def check_dependencies(self) -> Dict[str, ServiceHealth]:
        """检查依赖服务健康状态"""
        dependencies = {{}}{dependency_checks_code}
        
        return dependencies
    
    async def check_self(self) -> ServiceHealth:
        """检查自身服务健康状态"""
        try:
            # 检查服务基本功能
            uptime = self.get_uptime()
            
            details = {{
{details_code_str}
            }}
            
            # 确定状态
            status = HealthStatus.HEALTHY
            message = "{service_config['description']} is operational"
            
            return ServiceHealth(
                name=self.service_name,
                status=status,
                response_time_ms=0.0,
                message=message,
                details=details,
                timestamp=self.get_current_timestamp()
            )
            
        except Exception as e:
            return ServiceHealth(
                name=self.service_name,
                status=HealthStatus.ERROR,
                response_time_ms=0.0,
                message=f"Self check failed: {{str(e)}}",
                timestamp=self.get_current_timestamp()
            )
    
    def get_uptime(self) -> float:
        """获取服务运行时间"""
        import time
        return time.time() - self.start_time
    
    def get_current_timestamp(self) -> float:
        """获取当前时间戳"""
        import time
        return time.time()


# 全局健康检查器实例
_health_checker = None

def get_health_checker() -> {class_name}HealthChecker:
    """获取健康检查器实例（单例模式）"""
    global _health_checker
    if _health_checker is None:
        _health_checker = {class_name}HealthChecker()
    return _health_checker


async def get_health_status():
    """获取健康状态（FastAPI兼容）"""
    checker = get_health_checker()
    return await checker.get_health_status()


async def get_ready_status():
    """获取就绪状态（Kubernetes就绪探针）"""
    checker = get_health_checker()
    health = await checker.get_health_status()
    
    # 就绪检查更严格，关键依赖都必须健康
    if health.status in [HealthStatus.HEALTHY, HealthStatus.WARNING]:
        return {{
            "status": "ready",
            "service": "{service_name}",
            "timestamp": health.timestamp
        }}
    else:
        return {{
            "status": "not_ready",
            "service": "{service_name}",
            "message": health.message,
            "timestamp": health.timestamp
        }}


if __name__ == "__main__":
    import asyncio
    
    async def test_health_check():
        """测试健康检查功能"""
        print("🧪 测试{service_config['description']}健康检查...")
        
        checker = get_health_checker()
        health = await checker.get_health_status()
        
        print(f"整体状态: {{health.status}}")
        print(f"服务名称: {{health.service_name}}")
        print(f"版本: {{health.version}}")
        print(f"运行时间: {{health.uptime_seconds:.2f}}秒")
        print(f"消息: {{health.message}}")
        
        print("\\n服务检查结果:")
        for name, service in health.services.items():
            print(f"  {{name}}: {{service.status}} ({{service.response_time_ms:.2f}}ms)")
            if service.message:
                print(f"    消息: {{service.message}}")
        
        print(f"\\n系统状态:")
        print(f"  CPU: {{health.system.cpu_percent:.1f}}%")
        print(f"  内存: {{health.system.memory_percent:.1f}}%")
        print(f"  磁盘: {{health.system.disk_percent:.1f}}%")
        print(f"  系统状态: {{health.system.status}}")
        
        # 测试就绪状态
        ready = await get_ready_status()
        print(f"\\n就绪状态: {{ready['status']}}")
    
    asyncio.run(test_health_check())'''
    
    return template


def main():
    """主函数"""
    
    # 服务配置
    services_config = {
        "api_gateway": {
            "description": "API网关",
            "has_database": False,
            "has_redis": False,
            "dependencies": ["user_service", "topic_service", "document_service", 
                           "embedding_service", "llm_integration", "conversation_service", 
                           "summary_service", "manticore_search"],
            "self_check_details": [
                {"key": "proxy_timeout", "setting": "proxy_timeout", "default": "30"},
                {"key": "max_connections", "setting": "max_connections", "default": "100"},
                {"key": "backend_health_check_enabled", "setting": "enable_backend_health_check", "default": "True"}
            ]
        },
        "document_service": {
            "description": "文档服务",
            "has_database": True,
            "has_redis": False,
            "dependencies": ["embedding_service", "manticore_search"],
            "self_check_details": [
                {"key": "max_file_size", "setting": "max_file_size", "default": "10485760"},
                {"key": "async_processing_enabled", "setting": "enable_async_processing", "default": "True"},
                {"key": "max_concurrent_jobs", "setting": "max_concurrent_jobs", "default": "5"}
            ]
        },
        "llm_integration": {
            "description": "LLM集成服务",
            "has_database": False,
            "has_redis": True,
            "dependencies": ["manticore_search", "embedding_service"],
            "self_check_details": [
                {"key": "default_provider", "setting": "default_provider", "default": "'openai'"},
                {"key": "rag_enabled", "setting": "enable_rag", "default": "True"},
                {"key": "fallback_enabled", "setting": "enable_fallback", "default": "True"}
            ]
        },
        "conversation_service": {
            "description": "对话服务",
            "has_database": True,
            "has_redis": True,
            "dependencies": ["user_service", "topic_service", "llm_integration", "summary_service"],
            "self_check_details": [
                {"key": "websocket_enabled", "setting": "enable_websocket", "default": "True"},
                {"key": "max_connections", "setting": "max_websocket_connections", "default": "1000"},
                {"key": "message_queue_enabled", "setting": "enable_message_queue", "default": "True"}
            ]
        },
        "summary_service": {
            "description": "摘要服务",
            "has_database": True,
            "has_redis": True,
            "dependencies": ["llm_integration", "conversation_service", "document_service"],
            "self_check_details": [
                {"key": "auto_summary_enabled", "setting": "enable_auto_summary", "default": "True"},
                {"key": "batch_processing_enabled", "setting": "enable_batch_processing", "default": "True"},
                {"key": "max_concurrent_summaries", "setting": "max_concurrent_summaries", "default": "5"}
            ]
        }
    }
    
    project_root = Path(__file__).parent.parent.parent
    
    print("🏥 开始生成健康检查服务...")
    print("=" * 50)
    
    for service_name, config in services_config.items():
        # 生成健康检查服务代码
        health_service_code = generate_health_service(service_name, config)
        
        # 创建服务目录
        service_dir = project_root / service_name / "services"
        service_dir.mkdir(parents=True, exist_ok=True)
        
        # 写入健康检查服务文件
        health_file = service_dir / "health_service.py"
        with open(health_file, "w", encoding="utf-8") as f:
            f.write(health_service_code)
        
        print(f"✅ {service_name}/services/health_service.py 生成完成")
    
    print("\n🎯 所有健康检查服务生成完成！")
    print("下一步：更新各服务的API主文件以集成健康检查")


if __name__ == "__main__":
    main()
