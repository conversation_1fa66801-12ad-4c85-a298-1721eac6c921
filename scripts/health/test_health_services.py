#!/usr/bin/env python3
"""
健康检查服务测试脚本

测试所有服务的健康检查功能是否正常工作
"""

import sys
import os
import asyncio
import importlib
from pathlib import Path
from typing import Dict, Any

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))


async def test_service_health(service_name: str) -> Dict[str, Any]:
    """测试单个服务的健康检查"""
    try:
        # 动态导入健康检查模块
        module_path = f"{service_name}.services.health_service"
        health_module = importlib.import_module(module_path)
        
        # 获取健康状态
        health_status = await health_module.get_health_status()
        ready_status = await health_module.get_ready_status()
        
        return {
            "service": service_name,
            "status": "success",
            "health": {
                "overall_status": health_status.status,
                "uptime": health_status.uptime_seconds,
                "services_count": len(health_status.services),
                "system_cpu": health_status.system.cpu_percent,
                "system_memory": health_status.system.memory_percent,
                "message": health_status.message
            },
            "ready": ready_status["status"],
            "error": None
        }
        
    except ImportError as e:
        return {
            "service": service_name,
            "status": "import_error",
            "health": None,
            "ready": None,
            "error": f"Cannot import health module: {str(e)}"
        }
    except Exception as e:
        return {
            "service": service_name,
            "status": "error",
            "health": None,
            "ready": None,
            "error": str(e)
        }


async def test_all_health_services():
    """测试所有服务的健康检查"""
    
    # 需要测试的服务列表
    services = [
        "user_service",
        "topic_service", 
        "api_gateway",
        "document_service",
        "llm_integration",
        "conversation_service",
        "summary_service"
    ]
    
    print("🏥 开始测试所有服务的健康检查...")
    print("=" * 60)
    
    results = []
    
    # 并行测试所有服务
    tasks = [test_service_health(service) for service in services]
    test_results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 处理结果
    for i, result in enumerate(test_results):
        if isinstance(result, Exception):
            results.append({
                "service": services[i],
                "status": "exception",
                "health": None,
                "ready": None,
                "error": str(result)
            })
        else:
            results.append(result)
    
    # 显示结果
    success_count = 0
    for result in results:
        service_name = result["service"]
        status = result["status"]
        
        if status == "success":
            health = result["health"]
            ready = result["ready"]
            print(f"✅ {service_name:20} - 状态: {health['overall_status']:10} - 就绪: {ready:10} - 运行时间: {health['uptime']:.1f}s")
            success_count += 1
        elif status == "import_error":
            print(f"⚠️  {service_name:20} - 导入错误: {result['error']}")
        else:
            print(f"❌ {service_name:20} - 错误: {result['error']}")
    
    print("\n" + "=" * 60)
    print(f"测试完成: {success_count}/{len(services)} 服务健康检查正常")
    
    return results


def generate_health_report(results: list):
    """生成健康检查测试报告"""
    
    report = []
    report.append("# 健康检查服务测试报告")
    report.append("")
    report.append("## 📊 测试概览")
    report.append("")
    
    success_count = sum(1 for r in results if r["status"] == "success")
    total_count = len(results)
    
    report.append(f"- **总服务数**: {total_count}")
    report.append(f"- **测试成功**: {success_count}")
    report.append(f"- **测试失败**: {total_count - success_count}")
    report.append(f"- **成功率**: {success_count/total_count*100:.1f}%")
    report.append("")
    
    report.append("## 📋 详细结果")
    report.append("")
    report.append("| 服务名称 | 状态 | 整体健康 | 就绪状态 | 运行时间 | CPU | 内存 | 错误信息 |")
    report.append("|---------|------|----------|----------|----------|-----|------|----------|")
    
    for result in results:
        service = result["service"]
        status = result["status"]
        
        if status == "success":
            health = result["health"]
            ready = result["ready"]
            row = f"| {service} | ✅ 成功 | {health['overall_status']} | {ready} | {health['uptime']:.1f}s | {health['system_cpu']:.1f}% | {health['system_memory']:.1f}% | - |"
        else:
            error_msg = result["error"][:50] + "..." if len(result["error"]) > 50 else result["error"]
            row = f"| {service} | ❌ 失败 | - | - | - | - | - | {error_msg} |"
        
        report.append(row)
    
    report.append("")
    
    # 成功的服务详细信息
    successful_services = [r for r in results if r["status"] == "success"]
    if successful_services:
        report.append("## ✅ 成功的服务详情")
        report.append("")
        
        for result in successful_services:
            service = result["service"]
            health = result["health"]
            
            report.append(f"### {service}")
            report.append(f"- **整体状态**: {health['overall_status']}")
            report.append(f"- **运行时间**: {health['uptime']:.2f}秒")
            report.append(f"- **依赖服务数**: {health['services_count']}")
            report.append(f"- **系统CPU**: {health['system_cpu']:.1f}%")
            report.append(f"- **系统内存**: {health['system_memory']:.1f}%")
            report.append(f"- **消息**: {health['message']}")
            report.append("")
    
    # 失败的服务详细信息
    failed_services = [r for r in results if r["status"] != "success"]
    if failed_services:
        report.append("## ❌ 失败的服务详情")
        report.append("")
        
        for result in failed_services:
            service = result["service"]
            status = result["status"]
            error = result["error"]
            
            report.append(f"### {service}")
            report.append(f"- **状态**: {status}")
            report.append(f"- **错误**: {error}")
            report.append("")
    
    report.append("## 🔧 建议")
    report.append("")
    
    if success_count == total_count:
        report.append("🎉 所有服务的健康检查都正常工作！系统已准备好进入下一阶段。")
    else:
        report.append("⚠️ 部分服务的健康检查存在问题，建议：")
        report.append("1. 检查失败服务的依赖配置")
        report.append("2. 确保所有必要的模块都已正确安装")
        report.append("3. 验证服务配置文件的正确性")
    
    return "\n".join(report)


async def main():
    """主函数"""
    print("开始健康检查服务测试...")
    
    # 运行测试
    results = await test_all_health_services()
    
    # 生成报告
    report = generate_health_report(results)
    
    # 保存报告
    report_file = project_root / "scripts" / "health" / "health_test_report.md"
    with open(report_file, "w", encoding="utf-8") as f:
        f.write(report)
    
    print(f"\n📄 详细测试报告已保存到: {report_file}")
    
    # 返回测试结果
    success_count = sum(1 for r in results if r["status"] == "success")
    total_count = len(results)
    
    if success_count == total_count:
        print("\n🎉 所有健康检查服务测试通过！")
        return True
    else:
        print(f"\n⚠️ {total_count - success_count} 个服务测试失败，请查看报告详情")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
