"""
统一健康检查框架

为所有服务提供标准化的健康检查功能
"""

import time
import asyncio
import aiohttp
import psutil
from typing import Dict, Any, List, Optional
from enum import Enum
from pydantic import BaseModel
from abc import ABC, abstractmethod


class HealthStatus(str, Enum):
    """健康状态枚举"""
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    WARNING = "warning"
    ERROR = "error"


class ServiceHealth(BaseModel):
    """单个服务健康状态"""
    name: str
    status: HealthStatus
    response_time_ms: float
    message: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    timestamp: float


class SystemHealth(BaseModel):
    """系统健康状态"""
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    uptime_seconds: float
    status: HealthStatus


class OverallHealth(BaseModel):
    """整体健康状态响应"""
    status: HealthStatus
    service_name: str
    version: str
    uptime_seconds: float
    timestamp: float
    services: Dict[str, ServiceHealth]
    system: SystemHealth
    message: Optional[str] = None


class HealthChecker(ABC):
    """健康检查器抽象基类"""
    
    def __init__(self, service_name: str, version: str = "1.0.0"):
        self.service_name = service_name
        self.version = version
        self.start_time = time.time()
    
    @abstractmethod
    async def check_dependencies(self) -> Dict[str, ServiceHealth]:
        """检查依赖服务健康状态"""
        pass
    
    @abstractmethod
    async def check_self(self) -> ServiceHealth:
        """检查自身服务健康状态"""
        pass
    
    def check_system(self) -> SystemHealth:
        """检查系统健康状态"""
        try:
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            uptime = time.time() - psutil.boot_time()
            
            # 判断系统状态
            status = HealthStatus.HEALTHY
            if cpu_percent > 90 or memory.percent > 90 or disk.percent > 90:
                status = HealthStatus.WARNING
            if cpu_percent > 95 or memory.percent > 95 or disk.percent > 95:
                status = HealthStatus.UNHEALTHY
            
            return SystemHealth(
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                disk_percent=disk.percent,
                uptime_seconds=uptime,
                status=status
            )
        except Exception as e:
            return SystemHealth(
                cpu_percent=0.0,
                memory_percent=0.0,
                disk_percent=0.0,
                uptime_seconds=0.0,
                status=HealthStatus.ERROR
            )
    
    async def check_service_url(self, name: str, url: str, timeout: int = 5) -> ServiceHealth:
        """检查外部服务URL健康状态"""
        start_time = time.time()
        
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
                async with session.get(f"{url}/api/v1/health") as response:
                    response_time = (time.time() - start_time) * 1000
                    
                    if response.status == 200:
                        data = await response.json()
                        return ServiceHealth(
                            name=name,
                            status=HealthStatus.HEALTHY,
                            response_time_ms=response_time,
                            message="Service is healthy",
                            details=data,
                            timestamp=time.time()
                        )
                    else:
                        return ServiceHealth(
                            name=name,
                            status=HealthStatus.UNHEALTHY,
                            response_time_ms=response_time,
                            message=f"HTTP {response.status}",
                            timestamp=time.time()
                        )
        except asyncio.TimeoutError:
            response_time = timeout * 1000
            return ServiceHealth(
                name=name,
                status=HealthStatus.UNHEALTHY,
                response_time_ms=response_time,
                message="Request timeout",
                timestamp=time.time()
            )
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return ServiceHealth(
                name=name,
                status=HealthStatus.ERROR,
                response_time_ms=response_time,
                message=str(e),
                timestamp=time.time()
            )
    
    async def get_health_status(self) -> OverallHealth:
        """获取完整的健康状态"""
        try:
            # 并行检查所有依赖服务和自身
            tasks = []
            
            # 检查依赖服务
            dependencies_task = asyncio.create_task(self.check_dependencies())
            tasks.append(dependencies_task)
            
            # 检查自身
            self_task = asyncio.create_task(self.check_self())
            tasks.append(self_task)
            
            # 等待所有检查完成
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            dependencies = results[0] if not isinstance(results[0], Exception) else {}
            self_health = results[1] if not isinstance(results[1], Exception) else ServiceHealth(
                name=self.service_name,
                status=HealthStatus.ERROR,
                response_time_ms=0.0,
                message="Self check failed",
                timestamp=time.time()
            )
            
            # 添加自身健康状态
            all_services = {self.service_name: self_health}
            all_services.update(dependencies)
            
            # 检查系统状态
            system_health = self.check_system()
            
            # 确定整体状态
            overall_status = self._determine_overall_status(all_services, system_health)
            
            return OverallHealth(
                status=overall_status,
                service_name=self.service_name,
                version=self.version,
                uptime_seconds=time.time() - self.start_time,
                timestamp=time.time(),
                services=all_services,
                system=system_health,
                message=self._get_status_message(overall_status, all_services)
            )
            
        except Exception as e:
            return OverallHealth(
                status=HealthStatus.ERROR,
                service_name=self.service_name,
                version=self.version,
                uptime_seconds=time.time() - self.start_time,
                timestamp=time.time(),
                services={},
                system=SystemHealth(
                    cpu_percent=0.0,
                    memory_percent=0.0,
                    disk_percent=0.0,
                    uptime_seconds=0.0,
                    status=HealthStatus.ERROR
                ),
                message=f"Health check failed: {str(e)}"
            )
    
    def _determine_overall_status(self, services: Dict[str, ServiceHealth], system: SystemHealth) -> HealthStatus:
        """确定整体健康状态"""
        # 如果系统状态不健康，整体状态也不健康
        if system.status == HealthStatus.ERROR:
            return HealthStatus.ERROR
        if system.status == HealthStatus.UNHEALTHY:
            return HealthStatus.UNHEALTHY
        
        # 检查服务状态
        error_count = 0
        unhealthy_count = 0
        warning_count = 0
        
        for service in services.values():
            if service.status == HealthStatus.ERROR:
                error_count += 1
            elif service.status == HealthStatus.UNHEALTHY:
                unhealthy_count += 1
            elif service.status == HealthStatus.WARNING:
                warning_count += 1
        
        # 如果有错误，返回错误状态
        if error_count > 0:
            return HealthStatus.ERROR
        
        # 如果有不健康的服务，返回不健康状态
        if unhealthy_count > 0:
            return HealthStatus.UNHEALTHY
        
        # 如果有警告，返回警告状态
        if warning_count > 0 or system.status == HealthStatus.WARNING:
            return HealthStatus.WARNING
        
        return HealthStatus.HEALTHY
    
    def _get_status_message(self, status: HealthStatus, services: Dict[str, ServiceHealth]) -> str:
        """获取状态消息"""
        if status == HealthStatus.HEALTHY:
            return "All systems operational"
        elif status == HealthStatus.WARNING:
            return "Some systems showing warnings"
        elif status == HealthStatus.UNHEALTHY:
            unhealthy_services = [name for name, service in services.items() 
                                if service.status == HealthStatus.UNHEALTHY]
            return f"Unhealthy services: {', '.join(unhealthy_services)}"
        else:
            error_services = [name for name, service in services.items() 
                            if service.status == HealthStatus.ERROR]
            return f"Error in services: {', '.join(error_services)}"


class DatabaseHealthChecker:
    """数据库健康检查器"""
    
    @staticmethod
    async def check_postgresql(database_url: str, timeout: int = 5) -> ServiceHealth:
        """检查PostgreSQL数据库健康状态"""
        start_time = time.time()
        
        try:
            # 这里应该使用实际的数据库连接检查
            # 暂时返回模拟结果
            await asyncio.sleep(0.1)  # 模拟数据库查询
            response_time = (time.time() - start_time) * 1000
            
            return ServiceHealth(
                name="postgresql",
                status=HealthStatus.HEALTHY,
                response_time_ms=response_time,
                message="Database connection successful",
                details={"connection_pool": "active"},
                timestamp=time.time()
            )
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return ServiceHealth(
                name="postgresql",
                status=HealthStatus.ERROR,
                response_time_ms=response_time,
                message=str(e),
                timestamp=time.time()
            )
    
    @staticmethod
    async def check_redis(redis_url: str, timeout: int = 5) -> ServiceHealth:
        """检查Redis健康状态"""
        start_time = time.time()
        
        try:
            # 这里应该使用实际的Redis连接检查
            # 暂时返回模拟结果
            await asyncio.sleep(0.05)  # 模拟Redis ping
            response_time = (time.time() - start_time) * 1000
            
            return ServiceHealth(
                name="redis",
                status=HealthStatus.HEALTHY,
                response_time_ms=response_time,
                message="Redis connection successful",
                details={"ping": "pong"},
                timestamp=time.time()
            )
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return ServiceHealth(
                name="redis",
                status=HealthStatus.ERROR,
                response_time_ms=response_time,
                message=str(e),
                timestamp=time.time()
            )
