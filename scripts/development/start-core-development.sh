#!/bin/bash

# 知深学习导师 - 核心模块快速开发脚本
# 专注于立即开始开发topic_service和document_service

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 创建embedding_service的基础实现
create_embedding_service() {
    log_step "创建embedding_service基础实现..."
    
    cd embedding_service
    
    # 创建基础配置文件
    cat > requirements.txt << 'EOF'
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
sentence-transformers==2.2.2
numpy==1.24.3
redis==5.0.1
httpx==0.25.2
python-multipart==0.0.6
EOF

    # 创建基础配置
    cat > utils/config.py << 'EOF'
from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # 模型配置
    embedding_model: str = "sentence-transformers/all-MiniLM-L6-v2"
    embedding_dimension: int = 384
    
    # Redis配置
    redis_url: str = "redis://localhost:6379"
    cache_ttl: int = 3600
    
    # API配置
    api_host: str = "0.0.0.0"
    api_port: int = 8001
    
    class Config:
        env_prefix = "EMBEDDING_"

def get_settings() -> Settings:
    return Settings()
EOF

    # 创建基础模型
    cat > models/embedding.py << 'EOF'
from pydantic import BaseModel
from typing import List, Optional

class EmbeddingRequest(BaseModel):
    text: str
    model: Optional[str] = None

class EmbeddingResponse(BaseModel):
    embedding: List[float]
    dimension: int
    processing_time: float
    model: str

class BatchEmbeddingRequest(BaseModel):
    texts: List[str]
    model: Optional[str] = None

class BatchEmbeddingResponse(BaseModel):
    embeddings: List[List[float]]
    count: int
    processing_time: float
    model: str
EOF

    # 创建基础服务
    cat > services/embedding_service.py << 'EOF'
from sentence_transformers import SentenceTransformer
import time
import numpy as np
from typing import List
from ..models.embedding import EmbeddingRequest, EmbeddingResponse
from ..utils.config import Settings

class EmbeddingService:
    def __init__(self, settings: Settings):
        self.settings = settings
        self.model = SentenceTransformer(settings.embedding_model)
    
    def embed_text(self, request: EmbeddingRequest) -> EmbeddingResponse:
        start_time = time.time()
        
        # 生成向量
        embedding = self.model.encode(request.text)
        
        processing_time = (time.time() - start_time) * 1000
        
        return EmbeddingResponse(
            embedding=embedding.tolist(),
            dimension=len(embedding),
            processing_time=processing_time,
            model=self.settings.embedding_model
        )
    
    def embed_batch(self, texts: List[str]) -> List[List[float]]:
        embeddings = self.model.encode(texts)
        return [emb.tolist() for emb in embeddings]
EOF

    # 创建基础API
    cat > api/main.py << 'EOF'
from fastapi import FastAPI, Depends
from ..services.embedding_service import EmbeddingService
from ..models.embedding import EmbeddingRequest, EmbeddingResponse
from ..utils.config import get_settings, Settings

app = FastAPI(title="Embedding Service", version="1.0.0")

def get_embedding_service(settings: Settings = Depends(get_settings)) -> EmbeddingService:
    return EmbeddingService(settings)

@app.post("/api/v1/embed", response_model=EmbeddingResponse)
async def embed_text(
    request: EmbeddingRequest,
    service: EmbeddingService = Depends(get_embedding_service)
):
    return service.embed_text(request)

@app.get("/api/v1/health")
async def health_check():
    return {"status": "healthy", "service": "embedding_service"}
EOF

    # 创建测试脚本
    cat > test_module.py << 'EOF'
#!/usr/bin/env python3
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.embedding_service import EmbeddingService
from models.embedding import EmbeddingRequest
from utils.config import get_settings

def test_embedding_service():
    print("🧪 测试embedding_service...")
    
    settings = get_settings()
    service = EmbeddingService(settings)
    
    # 测试单文本向量化
    request = EmbeddingRequest(text="Hello, world!")
    response = service.embed_text(request)
    
    print(f"✅ 向量维度: {response.dimension}")
    print(f"✅ 处理时间: {response.processing_time:.2f}ms")
    print("✅ embedding_service 测试通过")

if __name__ == "__main__":
    test_embedding_service()
EOF

    chmod +x test_module.py
    
    cd ..
    log_success "embedding_service 基础实现创建完成"
}

# 创建user_service的简化实现
create_user_service() {
    log_step "创建user_service简化实现..."
    
    cd user_service
    
    # 创建基础配置文件
    cat > requirements.txt << 'EOF'
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
EOF

    # 创建用户模型
    cat > models/user.py << 'EOF'
from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class User(BaseModel):
    id: int
    username: str
    email: Optional[str] = None
    created_at: datetime
    is_active: bool = True

class UserResponse(BaseModel):
    user: User
    message: str
EOF

    # 创建用户服务
    cat > services/user_service.py << 'EOF'
from datetime import datetime
from ..models.user import User, UserResponse

class UserService:
    def __init__(self):
        # 固定的测试用户
        self.test_user = User(
            id=1,
            username="test_user",
            email="<EMAIL>",
            created_at=datetime.now(),
            is_active=True
        )
    
    def get_current_user(self) -> UserResponse:
        """返回固定的测试用户"""
        return UserResponse(
            user=self.test_user,
            message="返回固定测试用户"
        )
    
    def get_user_by_id(self, user_id: int) -> User:
        """根据ID获取用户（简化版只支持ID=1）"""
        if user_id == 1:
            return self.test_user
        return None
EOF

    # 创建API
    cat > api/main.py << 'EOF'
from fastapi import FastAPI, HTTPException
from ..services.user_service import UserService
from ..models.user import UserResponse, User

app = FastAPI(title="User Service (Simplified)", version="1.0.0")
user_service = UserService()

@app.get("/api/v1/users/me", response_model=UserResponse)
async def get_current_user():
    """获取当前用户（固定返回测试用户）"""
    return user_service.get_current_user()

@app.get("/api/v1/users/{user_id}", response_model=User)
async def get_user(user_id: int):
    """根据ID获取用户"""
    user = user_service.get_user_by_id(user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user

@app.get("/api/v1/health")
async def health_check():
    return {"status": "healthy", "service": "user_service"}
EOF

    cd ..
    log_success "user_service 简化实现创建完成"
}

# 创建topic_service的基础实现
create_topic_service() {
    log_step "创建topic_service基础实现..."
    
    cd topic_service
    
    # 创建基础配置文件
    cat > requirements.txt << 'EOF'
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
sqlalchemy==2.0.23
psycopg2-binary==2.9.9
EOF

    # 创建主题模型
    cat > models/topic.py << 'EOF'
from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class TopicBase(BaseModel):
    title: str
    description: Optional[str] = None

class TopicCreate(TopicBase):
    user_id: int = 1  # 固定测试用户

class Topic(TopicBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime
    is_active: bool = True
    document_count: int = 0

class TopicResponse(BaseModel):
    topics: list[Topic]
    total: int
    limit: int
    offset: int
EOF

    cd ..
    log_success "topic_service 基础实现创建完成"
}

# 主函数
main() {
    echo "=========================================="
    echo "    知深学习导师 - 核心模块快速开发"
    echo "=========================================="
    echo ""
    
    log_info "开始创建核心模块的基础实现..."
    echo ""
    
    # 检查是否在项目根目录
    if [ ! -f "ARCHITECTURE.md" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 创建基础实现
    create_embedding_service
    create_user_service
    create_topic_service
    
    echo ""
    log_success "🎉 核心模块基础实现创建完成！"
    echo ""
    
    echo "=== 下一步操作 ==="
    echo "1. 启动基础服务:"
    echo "   ./scripts/quick-start.sh"
    echo ""
    echo "2. 测试embedding_service:"
    echo "   cd embedding_service"
    echo "   python test_module.py"
    echo ""
    echo "3. 启动各个服务:"
    echo "   # Terminal 1: embedding_service"
    echo "   cd embedding_service && uvicorn api.main:app --reload --port 8001"
    echo ""
    echo "   # Terminal 2: user_service"
    echo "   cd user_service && uvicorn api.main:app --reload --port 8002"
    echo ""
    echo "4. 继续开发topic_service和document_service的完整功能"
}

# 执行主函数
main "$@"
