# -*- coding: utf-8 -*-
"""
端到端（End-to-End）流程验证脚本

本脚本旨在用最快的方式，模拟并验证整个系统的核心业务流程：
1. 上传文档 -> 2. 文档处理与入库 -> 3. 围绕文档进行问答

这有助于在早期发现核心路径上的问题，而无需等待所有服务和UI的完整开发。
"""
import os
import time
import requests
import uuid

# --- 配置服务地址 ---
# 在实际运行前，请确保所有相关服务已在本地启动
# 您可以根据docker-compose.yml或本地运行的端口进行修改
API_GATEWAY_URL = "http://localhost:8000"  # 假设API网关在8000端口
DOCUMENT_SERVICE_URL = f"{API_GATEWAY_URL}/api/v1/documents"
CONVERSATION_SERVICE_URL = f"{API_GATEWAY_URL}/api/v1/conversations"

# --- 模拟数据 ---
TEST_USER_ID = "test_user_001"
TEST_TOPIC_ID = str(uuid.uuid4())
TEST_DOCUMENT_CONTENT = """
光合作用是植物、藻类和某些细菌，在可见光的照射下，
经过光反应和暗反应，利用光合色素，将二氧化碳和水转化为有机物，
并释放出氧气的生化过程。
对于生物界的几乎所有生物来说，这个过程是它们赖以生存的关键。
"""

def run_e2e_test():
    """
    执行端到端测试流程
    """
    print("🚀 [1/4] 开始端到端流程验证...")

    # === 步骤 1: 模拟文档上传 ===
    # 我们将直接调用 document_service 的接口来创建一个新文档
    # 在一个完整的实现中，这里可能是一个文件上传的HTTP请求
    print("\n📄 [2/4] 模拟上传文档...")
    try:
        doc_payload = {
            "topic_id": TEST_TOPIC_ID,
            "user_id": TEST_USER_ID,
            "content": TEST_DOCUMENT_CONTENT,
            "filename": "photosynthesis.txt"
        }
        # 注意：这里的接口路径`/documents/content`需要与document_service中的实现对应
        response = requests.post(f"{DOCUMENT_SERVICE_URL}/content", json=doc_payload)
        response.raise_for_status()  # 如果请求失败，则抛出异常
        document_id = response.json().get("document_id")
        print(f"✅ 文档上传成功! Document ID: {document_id}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 文档上传失败: {e}")
        print("   请检查 document_service 和 api_gateway 是否已正确运行。")
        return

    # === 步骤 2: 等待文档处理 ===
    # 在真实的异步系统中，我们需要一个机制来确认文档已处理完毕
    # 这里我们简单地等待几秒钟，模拟后台处理时间
    print("\n⏳ [3/4] 等待文档处理与向量化...")
    processing_time = 10  # 假设处理需要10秒
    for i in range(processing_time):
        time.sleep(1)
        print(f"   ... {processing_time - i - 1} 秒", end="\r")
    print("\n✅ 假设文档处理完成。")

    # === 步骤 3: 模拟围绕文档进行提问 ===
    # 我们将调用对话服务的接口，提出一个与文档内容相关的问题
    print("\n💬 [4/4] 模拟提问...")
    try:
        qa_payload = {
            "topic_id": TEST_TOPIC_ID,
            "user_id": TEST_USER_ID,
            "question": "请根据我上传的资料，解释一下什么是光合作用？"
        }
        # 注意：这里的接口路径需要与conversation_service或llm_integration的实现对应
        response = requests.post(CONVERSATION_SERVICE_URL, json=qa_payload)
        response.raise_for_status()
        ai_answer = response.json().get("answer")

        print("\n🎉 端到端流程验证成功!")
        print("===================================")
        print(f"🤔 用户提问: {qa_payload['question']}")
        print(f"🤖 AI 回答: {ai_answer}")
        print("===================================")

        # 检查回答是否利用了上下文
        if "二氧化碳" in ai_answer and "氧气" in ai_answer:
            print("👍 AI的回答包含了上下文中的关键词，RAG流程可能已跑通！")
        else:
            print("⚠️ AI的回答似乎没有利用我们上传的文档，请检查RAG流程。")

    except requests.exceptions.RequestException as e:
        print(f"❌ 提问失败: {e}")
        print("   请检查 conversation_service, llm_integration 和 manticore_search 是否已正确运行。")
        return

if __name__ == "__main__":
    # 在运行此脚本前，请确保：
    # 1. 相关的服务 (api_gateway, document_service, embedding_service, llm_integration, manticore_search) 已经启动。
    # 2. 服务监听的地址和端口与脚本中的配置一致。
    # 3. 必要的环境变量（如OPENAI_API_KEY）已经设置。
    run_e2e_test()