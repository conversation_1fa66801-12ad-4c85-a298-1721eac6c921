#!/usr/bin/env python3
"""
批量更新服务间URL引用脚本

更新所有配置文件中的服务URL引用，从8000-8008端口更新为9000-9008
"""

import os
import re
from pathlib import Path

def update_service_urls_in_configs():
    """更新配置文件中的服务URL"""
    
    # URL映射：旧URL -> 新URL
    url_mapping = {
        "http://localhost:8000": "http://localhost:9000",  # manticore_search
        "http://localhost:8001": "http://localhost:9001",  # embedding_service
        "http://localhost:8002": "http://localhost:9002",  # user_service
        "http://localhost:8003": "http://localhost:9003",  # api_gateway
        "http://localhost:8004": "http://localhost:9004",  # topic_service
        "http://localhost:8005": "http://localhost:9005",  # document_service
        "http://localhost:8006": "http://localhost:9006",  # llm_integration
        "http://localhost:8007": "http://localhost:9007",  # conversation_service
        "http://localhost:8008": "http://localhost:9008",  # summary_service
    }
    
    project_root = Path(__file__).parent.parent.parent
    
    # 需要更新的配置文件
    config_files = [
        "user_service/utils/config.py",
        "topic_service/utils/config.py",
        "document_service/utils/config.py",
        "llm_integration/utils/config.py",
        "conversation_service/utils/config.py",
        "summary_service/utils/config.py",
    ]
    
    print("🔄 开始更新配置文件中的服务URL...")
    print("=" * 50)
    
    for config_file in config_files:
        file_path = project_root / config_file
        
        if not file_path.exists():
            print(f"⚠️  {config_file} 不存在，跳过")
            continue
        
        # 读取文件内容
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # 替换URL
        updated = False
        for old_url, new_url in url_mapping.items():
            if old_url in content:
                content = content.replace(old_url, new_url)
                updated = True
        
        if updated:
            # 写回文件
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            print(f"✅ {config_file} URL更新完成")
        else:
            print(f"ℹ️  {config_file} 无需更新")


def update_env_example_urls():
    """更新.env.example文件中的服务URL"""
    
    url_mapping = {
        "http://localhost:8000": "http://localhost:9000",
        "http://localhost:8001": "http://localhost:9001",
        "http://localhost:8002": "http://localhost:9002",
        "http://localhost:8003": "http://localhost:9003",
        "http://localhost:8004": "http://localhost:9004",
        "http://localhost:8005": "http://localhost:9005",
        "http://localhost:8006": "http://localhost:9006",
        "http://localhost:8007": "http://localhost:9007",
        "http://localhost:8008": "http://localhost:9008",
    }
    
    project_root = Path(__file__).parent.parent.parent
    
    # 需要更新的.env.example文件
    env_files = [
        "api_gateway/.env.example",
        "topic_service/.env.example",
        "document_service/.env.example",
        "llm_integration/.env.example",
        "conversation_service/.env.example",
        "summary_service/.env.example",
    ]
    
    print("\n🔄 开始更新.env.example文件中的服务URL...")
    print("=" * 50)
    
    for env_file in env_files:
        file_path = project_root / env_file
        
        if not file_path.exists():
            print(f"⚠️  {env_file} 不存在，跳过")
            continue
        
        # 读取文件内容
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # 替换URL
        updated = False
        for old_url, new_url in url_mapping.items():
            if old_url in content:
                content = content.replace(old_url, new_url)
                updated = True
        
        if updated:
            # 写回文件
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            print(f"✅ {env_file} URL更新完成")
        else:
            print(f"ℹ️  {env_file} 无需更新")


def main():
    """主函数"""
    print("开始服务URL批量更新...")
    
    # 更新配置文件
    update_service_urls_in_configs()
    
    # 更新.env.example文件
    update_env_example_urls()
    
    print("\n🎉 所有服务URL更新完成！")
    print("新的端口范围：9000-9008")


if __name__ == "__main__":
    main()
