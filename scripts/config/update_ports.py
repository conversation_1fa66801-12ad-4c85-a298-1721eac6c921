#!/usr/bin/env python3
"""
批量更新端口号脚本

将所有服务的端口从8000-8008更新为9000-9008
"""

import os
from pathlib import Path

def update_env_file_ports():
    """更新所有.env.example文件中的端口号"""
    
    # 端口映射：旧端口 -> 新端口
    port_mapping = {
        "8000": "9000",  # manticore_search
        "8001": "9001",  # embedding_service
        "8002": "9002",  # user_service
        "8003": "9003",  # api_gateway
        "8004": "9004",  # topic_service
        "8005": "9005",  # document_service
        "8006": "9006",  # llm_integration
        "8007": "9007",  # conversation_service
        "8008": "9008",  # summary_service
    }
    
    # 服务列表
    services = [
        "manticore_search",
        "embedding_service", 
        "user_service",
        "api_gateway",
        "topic_service",
        "document_service",
        "llm_integration",
        "conversation_service",
        "summary_service"
    ]
    
    project_root = Path(__file__).parent.parent.parent
    
    print("🔄 开始批量更新端口号...")
    print("=" * 50)
    
    for service in services:
        env_file = project_root / service / ".env.example"
        
        if not env_file.exists():
            print(f"⚠️  {service}/.env.example 不存在，跳过")
            continue
        
        # 读取文件内容
        with open(env_file, "r", encoding="utf-8") as f:
            content = f.read()
        
        # 替换端口号
        updated = False
        for old_port, new_port in port_mapping.items():
            if old_port in content:
                content = content.replace(old_port, new_port)
                updated = True
        
        if updated:
            # 写回文件
            with open(env_file, "w", encoding="utf-8") as f:
                f.write(content)
            print(f"✅ {service}/.env.example 端口更新完成")
        else:
            print(f"ℹ️  {service}/.env.example 无需更新")
    
    print("\n🎯 端口更新完成！")


def update_architecture_docs():
    """更新架构文档中的端口信息"""
    
    project_root = Path(__file__).parent.parent.parent
    
    # 更新README.md
    readme_file = project_root / "README.md"
    if readme_file.exists():
        with open(readme_file, "r", encoding="utf-8") as f:
            content = f.read()
        
        # 替换服务端口分配部分
        old_ports = """### 服务端口分配
- **manticore_search**: 8000 (已完成)
- **embedding_service**: 8001
- **user_service**: 8002
- **api_gateway**: 8003
- **topic_service**: 8004
- **document_service**: 8005
- **llm_integration**: 8006
- **conversation_service**: 8007
- **summary_service**: 8008
- **web_ui**: 3000"""
        
        new_ports = """### 服务端口分配
- **manticore_search**: 9000 (已完成)
- **embedding_service**: 9001
- **user_service**: 9002
- **api_gateway**: 9003
- **topic_service**: 9004
- **document_service**: 9005
- **llm_integration**: 9006
- **conversation_service**: 9007
- **summary_service**: 9008
- **web_ui**: 3000"""
        
        if old_ports in content:
            content = content.replace(old_ports, new_ports)
            with open(readme_file, "w", encoding="utf-8") as f:
                f.write(content)
            print("✅ README.md 端口信息更新完成")
    
    # 更新docker-compose.yml中的端口映射
    compose_file = project_root / "docker-compose.yml"
    if compose_file.exists():
        with open(compose_file, "r", encoding="utf-8") as f:
            content = f.read()
        
        # 替换端口映射
        port_replacements = [
            ('"8000:8000"', '"9000:9000"'),
            ('"8001:8001"', '"9001:9001"'),
            ('"8002:8002"', '"9002:9002"'),
            ('"8003:8003"', '"9003:9003"'),
            ('"8004:8004"', '"9004:9004"'),
            ('"8005:8005"', '"9005:9005"'),
            ('"8006:8006"', '"9006:9006"'),
            ('"8007:8007"', '"9007:9007"'),
            ('"8008:8008"', '"9008:9008"'),
        ]
        
        updated = False
        for old_mapping, new_mapping in port_replacements:
            if old_mapping in content:
                content = content.replace(old_mapping, new_mapping)
                updated = True
        
        if updated:
            with open(compose_file, "w", encoding="utf-8") as f:
                f.write(content)
            print("✅ docker-compose.yml 端口映射更新完成")


def main():
    """主函数"""
    print("开始端口号批量更新...")
    
    # 更新.env.example文件
    update_env_file_ports()
    
    # 更新架构文档
    update_architecture_docs()
    
    print("\n🎉 所有端口号更新完成！")
    print("新的端口范围：9000-9008 (避免与常见服务冲突)")


if __name__ == "__main__":
    main()
