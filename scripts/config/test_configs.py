#!/usr/bin/env python3
"""
配置实际加载测试脚本

测试所有服务的配置是否能正确加载和工作
"""

import sys
import os
from pathlib import Path
from typing import Dict, Any

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

def test_config_loading():
    """测试所有服务的配置加载"""
    
    services_config = [
        ("embedding_service", "embedding_service.utils.config"),
        ("manticore_search", "manticore_search.utils.config"),
        ("user_service", "user_service.utils.config"),
        ("topic_service", "topic_service.utils.config"),
        ("api_gateway", "api_gateway.utils.config"),
        ("document_service", "document_service.utils.config"),
        ("llm_integration", "llm_integration.utils.config"),
        ("conversation_service", "conversation_service.utils.config"),
        ("summary_service", "summary_service.utils.config"),
    ]
    
    results = {}
    
    print("🧪 开始测试配置加载...")
    print("=" * 60)
    
    for service_name, config_module in services_config:
        try:
            # 动态导入配置模块
            module = __import__(config_module, fromlist=['get_settings'])
            settings = module.get_settings()
            
            # 收集关键配置信息
            config_info = {
                "api_port": getattr(settings, 'api_port', 'N/A'),
                "api_host": getattr(settings, 'api_host', 'N/A'),
                "log_level": getattr(settings, 'log_level', 'N/A'),
                "environment": getattr(settings, 'environment', 'N/A'),
                "debug": getattr(settings, 'debug', 'N/A'),
            }
            
            # 添加服务特定的配置信息
            if service_name == "embedding_service":
                config_info.update({
                    "openai_model": getattr(settings, 'default_model', 'N/A'),
                    "cache_enabled": getattr(settings, 'enable_cache', 'N/A'),
                })
            elif service_name == "user_service":
                config_info.update({
                    "jwt_expire": getattr(settings, 'jwt_expire_minutes', 'N/A'),
                    "max_login_attempts": getattr(settings, 'max_login_attempts', 'N/A'),
                })
            elif service_name == "api_gateway":
                config_info.update({
                    "proxy_timeout": getattr(settings, 'proxy_timeout', 'N/A'),
                    "max_connections": getattr(settings, 'max_connections', 'N/A'),
                })
            elif service_name == "llm_integration":
                config_info.update({
                    "default_provider": getattr(settings, 'default_provider', 'N/A'),
                    "rag_enabled": getattr(settings, 'enable_rag', 'N/A'),
                })
            
            results[service_name] = {
                "status": "success",
                "config": config_info,
                "message": "配置加载成功"
            }
            
            print(f"✅ {service_name:20} - 端口: {config_info['api_port']:5} - 环境: {config_info['environment']}")
            
        except Exception as e:
            results[service_name] = {
                "status": "error",
                "config": {},
                "message": str(e)
            }
            print(f"❌ {service_name:20} - 错误: {str(e)[:50]}...")
    
    return results


def test_port_uniqueness(results: Dict[str, Any]):
    """测试端口唯一性"""
    print("\n🔌 端口分配检查:")
    print("-" * 40)
    
    ports = {}
    conflicts = []
    
    for service_name, result in results.items():
        if result["status"] == "success":
            port = result["config"].get("api_port")
            if port and port != "N/A":
                if port in ports:
                    conflicts.append(f"端口 {port}: {ports[port]} 和 {service_name}")
                else:
                    ports[port] = service_name
                    print(f"  {service_name:20} -> 端口 {port}")
    
    if conflicts:
        print("\n❌ 发现端口冲突:")
        for conflict in conflicts:
            print(f"  {conflict}")
        return False
    else:
        print("\n✅ 所有端口分配正确，无冲突")
        return True


def test_environment_consistency(results: Dict[str, Any]):
    """测试环境配置一致性"""
    print("\n🌍 环境配置一致性检查:")
    print("-" * 40)
    
    environments = {}
    for service_name, result in results.items():
        if result["status"] == "success":
            env = result["config"].get("environment", "unknown")
            if env not in environments:
                environments[env] = []
            environments[env].append(service_name)
    
    if len(environments) == 1:
        env_name = list(environments.keys())[0]
        print(f"✅ 所有服务使用统一环境: {env_name}")
        return True
    else:
        print("⚠️  发现环境配置不一致:")
        for env, services in environments.items():
            print(f"  {env}: {', '.join(services)}")
        return False


def generate_config_summary(results: Dict[str, Any]):
    """生成配置摘要报告"""
    print("\n📊 配置摘要报告:")
    print("=" * 60)
    
    success_count = sum(1 for r in results.values() if r["status"] == "success")
    total_count = len(results)
    
    print(f"总服务数: {total_count}")
    print(f"配置成功: {success_count}")
    print(f"配置失败: {total_count - success_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print("\n🎉 所有服务配置测试通过！")
        return True
    else:
        print("\n⚠️  部分服务配置存在问题，请检查错误信息")
        return False


def main():
    """主函数"""
    print("开始配置实际加载测试...")
    
    # 测试配置加载
    results = test_config_loading()
    
    # 测试端口唯一性
    port_ok = test_port_uniqueness(results)
    
    # 测试环境一致性
    env_ok = test_environment_consistency(results)
    
    # 生成摘要报告
    summary_ok = generate_config_summary(results)
    
    # 保存详细结果
    import json
    report_file = project_root / "scripts" / "config" / "config_test_report.json"
    with open(report_file, "w", encoding="utf-8") as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 详细测试报告已保存到: {report_file}")
    
    # 返回总体状态
    overall_success = summary_ok and port_ok and env_ok
    if overall_success:
        print("\n🎯 配置标准化第一阶段完成！所有测试通过。")
    else:
        print("\n🔧 配置标准化需要进一步调整。")
    
    return overall_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
