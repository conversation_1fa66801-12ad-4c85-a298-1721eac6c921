{"embedding_service": {"status": "success", "config": {"api_port": 9001, "api_host": "0.0.0.0", "log_level": "INFO", "environment": "development", "debug": false, "openai_model": "text-embedding-ada-002", "cache_enabled": false}, "message": "配置加载成功"}, "manticore_search": {"status": "success", "config": {"api_port": 9000, "api_host": "0.0.0.0", "log_level": "INFO", "environment": "development", "debug": false}, "message": "配置加载成功"}, "user_service": {"status": "success", "config": {"api_port": 9002, "api_host": "0.0.0.0", "log_level": "INFO", "environment": "development", "debug": false, "jwt_expire": 1440, "max_login_attempts": 5}, "message": "配置加载成功"}, "topic_service": {"status": "success", "config": {"api_port": 9004, "api_host": "0.0.0.0", "log_level": "INFO", "environment": "development", "debug": false}, "message": "配置加载成功"}, "api_gateway": {"status": "success", "config": {"api_port": 9003, "api_host": "0.0.0.0", "log_level": "INFO", "environment": "development", "debug": false, "proxy_timeout": 30, "max_connections": 100}, "message": "配置加载成功"}, "document_service": {"status": "success", "config": {"api_port": 9005, "api_host": "0.0.0.0", "log_level": "INFO", "environment": "development", "debug": false}, "message": "配置加载成功"}, "llm_integration": {"status": "success", "config": {"api_port": 9006, "api_host": "0.0.0.0", "log_level": "INFO", "environment": "development", "debug": false, "default_provider": "openai", "rag_enabled": true}, "message": "配置加载成功"}, "conversation_service": {"status": "success", "config": {"api_port": 9007, "api_host": "0.0.0.0", "log_level": "INFO", "environment": "development", "debug": false}, "message": "配置加载成功"}, "summary_service": {"status": "success", "config": {"api_port": 9008, "api_host": "0.0.0.0", "log_level": "INFO", "environment": "development", "debug": false}, "message": "配置加载成功"}}