#!/usr/bin/env python3
"""
配置验证脚本

验证所有服务的配置文件是否正确加载和格式化
"""

import sys
import os
from pathlib import Path
from typing import Dict, List, Tuple

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

def test_service_config(service_name: str, config_module: str) -> Tuple[bool, str]:
    """
    测试单个服务的配置加载
    
    Args:
        service_name: 服务名称
        config_module: 配置模块路径
    
    Returns:
        (是否成功, 错误信息)
    """
    try:
        # 动态导入配置模块
        module = __import__(config_module, fromlist=['get_settings'])
        settings = module.get_settings()
        
        # 基础验证
        assert hasattr(settings, 'api_port'), f"{service_name}: 缺少api_port配置"
        assert hasattr(settings, 'api_host'), f"{service_name}: 缺少api_host配置"
        assert hasattr(settings, 'log_level'), f"{service_name}: 缺少log_level配置"
        
        # 端口范围验证
        assert 1024 <= settings.api_port <= 65535, f"{service_name}: api_port超出有效范围"
        
        # 日志级别验证
        valid_log_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        assert settings.log_level in valid_log_levels, f"{service_name}: 无效的log_level"
        
        return True, f"✅ {service_name} 配置验证通过"
        
    except ImportError as e:
        return False, f"❌ {service_name} 配置模块导入失败: {e}"
    except Exception as e:
        return False, f"❌ {service_name} 配置验证失败: {e}"


def check_env_example_files() -> List[Tuple[str, bool, str]]:
    """检查所有服务的.env.example文件是否存在"""
    results = []
    
    services = [
        'embedding_service',
        'manticore_search', 
        'user_service',
        'topic_service',
        'api_gateway',
        'document_service',
        'llm_integration',
        'conversation_service',
        'summary_service'
    ]
    
    for service in services:
        env_file = project_root / service / '.env.example'
        if env_file.exists():
            results.append((service, True, f"✅ {service}/.env.example 存在"))
        else:
            results.append((service, False, f"❌ {service}/.env.example 缺失"))
    
    return results


def check_config_files() -> List[Tuple[str, bool, str]]:
    """检查所有服务的config.py文件是否存在"""
    results = []
    
    services_with_config = [
        ('embedding_service', 'embedding_service.utils.config'),
        ('manticore_search', 'manticore_search.utils.config'),
        ('user_service', 'user_service.utils.config'),
        ('topic_service', 'topic_service.utils.config'),
        ('api_gateway', 'api_gateway.utils.config'),
    ]
    
    for service, config_module in services_with_config:
        config_file = project_root / service / 'utils' / 'config.py'
        if config_file.exists():
            # 测试配置加载
            success, message = test_service_config(service, config_module)
            results.append((service, success, message))
        else:
            results.append((service, False, f"❌ {service}/utils/config.py 缺失"))
    
    return results


def check_port_conflicts() -> List[str]:
    """检查端口冲突"""
    port_mapping = {
        9000: 'manticore_search',
        9001: 'embedding_service',
        9002: 'user_service',
        9003: 'api_gateway',
        9004: 'topic_service',
        9005: 'document_service',
        9006: 'llm_integration',
        9007: 'conversation_service',
        9008: 'summary_service',
        3000: 'web_ui'
    }
    
    conflicts = []
    used_ports = set()
    
    for port, service in port_mapping.items():
        if port in used_ports:
            conflicts.append(f"端口冲突: {port} 被多个服务使用")
        used_ports.add(port)
    
    return conflicts


def generate_summary_report() -> str:
    """生成配置状态总结报告"""
    report = []
    report.append("=" * 60)
    report.append("配置状态总结报告")
    report.append("=" * 60)
    
    # 检查配置文件
    report.append("\n📁 配置文件检查:")
    config_results = check_config_files()
    success_count = sum(1 for _, success, _ in config_results if success)
    total_count = len(config_results)
    
    for service, success, message in config_results:
        report.append(f"  {message}")
    
    report.append(f"\n配置文件状态: {success_count}/{total_count} 通过")
    
    # 检查环境变量示例文件
    report.append("\n📄 环境变量示例文件检查:")
    env_results = check_env_example_files()
    env_success_count = sum(1 for _, success, _ in env_results if success)
    env_total_count = len(env_results)
    
    for service, success, message in env_results:
        report.append(f"  {message}")
    
    report.append(f"\n环境变量文件状态: {env_success_count}/{env_total_count} 存在")
    
    # 检查端口冲突
    report.append("\n🔌 端口分配检查:")
    conflicts = check_port_conflicts()
    if conflicts:
        for conflict in conflicts:
            report.append(f"  ❌ {conflict}")
    else:
        report.append("  ✅ 无端口冲突")
    
    # 总体状态
    report.append("\n" + "=" * 60)
    overall_success = (success_count == total_count and 
                      env_success_count == env_total_count and 
                      len(conflicts) == 0)
    
    if overall_success:
        report.append("🎉 所有配置检查通过！系统配置标准化完成。")
    else:
        report.append("⚠️  存在配置问题，请根据上述报告进行修复。")
    
    report.append("=" * 60)
    
    return "\n".join(report)


def main():
    """主函数"""
    print("开始配置验证...")
    
    # 生成并打印报告
    report = generate_summary_report()
    print(report)
    
    # 保存报告到文件
    report_file = project_root / "scripts" / "config" / "validation_report.txt"
    with open(report_file, "w", encoding="utf-8") as f:
        f.write(report)
    
    print(f"\n📊 详细报告已保存到: {report_file}")


if __name__ == "__main__":
    main()
