#!/bin/bash

# 知深学习导师 - 快速启动脚本
# 用于快速搭建开发环境和启动已完成的模块

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Python版本
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 未安装，请先安装 Python 3.9+"
        exit 1
    fi
    
    python_version=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
    if [[ $(echo "$python_version < 3.9" | bc -l) -eq 1 ]]; then
        log_error "Python 版本过低 ($python_version)，需要 3.9+"
        exit 1
    fi
    log_success "Python 版本检查通过: $python_version"
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    log_success "Docker 检查通过"
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    log_success "Docker Compose 检查通过"
}

# 设置虚拟环境
setup_venv() {
    log_info "设置Python虚拟环境..."
    
    if [ ! -d ".venv" ]; then
        python3 -m venv .venv
        log_success "虚拟环境创建成功"
    else
        log_info "虚拟环境已存在"
    fi
    
    # 激活虚拟环境
    source .venv/bin/activate
    log_success "虚拟环境已激活"
    
    # 升级pip
    pip install --upgrade pip
    
    # 安装依赖
    log_info "安装Python依赖包..."
    pip install -r requirements.txt
    log_success "依赖包安装完成"
}

# 启动基础服务
start_infrastructure() {
    log_info "启动基础设施服务..."
    
    # 启动PostgreSQL, Redis, Manticore
    docker-compose up -d postgres redis manticore
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    if docker-compose ps | grep -q "postgres.*Up"; then
        log_success "PostgreSQL 启动成功"
    else
        log_error "PostgreSQL 启动失败"
        exit 1
    fi
    
    if docker-compose ps | grep -q "redis.*Up"; then
        log_success "Redis 启动成功"
    else
        log_error "Redis 启动失败"
        exit 1
    fi
    
    if docker-compose ps | grep -q "manticore.*Up"; then
        log_success "Manticore Search 启动成功"
    else
        log_error "Manticore Search 启动失败"
        exit 1
    fi
}

# 测试已完成的模块
test_completed_modules() {
    log_info "测试已完成的模块..."
    
    # 测试manticore_search模块
    log_info "测试 manticore_search 模块..."
    cd manticore_search
    
    # 运行模块测试
    if python test_module.py; then
        log_success "manticore_search 模块测试通过"
    else
        log_error "manticore_search 模块测试失败"
        cd ..
        exit 1
    fi
    
    cd ..
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    # 等待PostgreSQL完全启动
    sleep 5
    
    # 创建数据库表结构
    docker-compose exec postgres psql -U master_know_user -d master_know -c "
        CREATE TABLE IF NOT EXISTS system_info (
            id SERIAL PRIMARY KEY,
            version VARCHAR(50),
            initialized_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        INSERT INTO system_info (version) VALUES ('1.0.0') ON CONFLICT DO NOTHING;
    "
    
    log_success "数据库初始化完成"
}

# 显示服务状态
show_status() {
    log_info "服务状态概览:"
    echo ""
    echo "=== 基础设施服务 ==="
    echo "PostgreSQL:     http://localhost:5432"
    echo "Redis:          redis://localhost:6379"
    echo "Manticore:      http://localhost:9308"
    echo ""
    echo "=== 应用服务 (按开发优先级) ==="
    echo "✅ manticore_search:    已完成"
    echo "🔥 embedding_service:   待开发 (Priority 1)"
    echo "👤 user_service:        待开发 (Priority 2)"
    echo "🚪 api_gateway:         待开发 (Priority 3)"
    echo "📚 topic_service:       待开发 (Priority 4)"
    echo "📄 document_service:    待开发 (Priority 5)"
    echo "🤖 llm_integration:     待开发 (Priority 6)"
    echo "💬 conversation_service: 待开发 (Priority 7)"
    echo "📝 summary_service:     待开发 (Priority 8)"
    echo "🖥️ web_ui:              待开发 (Priority 9)"
    echo ""
    echo "=== 下一步操作 ==="
    echo "1. 开始开发第一个模块:"
    echo "   cd embedding_service"
    echo "   # 按照 README.md 进行开发"
    echo ""
    echo "2. 查看架构文档:"
    echo "   cat ARCHITECTURE.md"
    echo ""
    echo "3. 停止所有服务:"
    echo "   docker-compose down"
}

# 主函数
main() {
    echo "=========================================="
    echo "    知深学习导师 - 快速启动脚本"
    echo "=========================================="
    echo ""
    
    # 检查是否在项目根目录
    if [ ! -f "ARCHITECTURE.md" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 执行启动流程
    check_dependencies
    setup_venv
    start_infrastructure
    init_database
    test_completed_modules
    
    echo ""
    log_success "🎉 快速启动完成！"
    echo ""
    show_status
}

# 错误处理
trap 'log_error "脚本执行失败，请检查错误信息"; exit 1' ERR

# 执行主函数
main "$@"
