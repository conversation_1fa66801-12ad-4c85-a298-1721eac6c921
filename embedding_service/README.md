# 向量化引擎 (Embedding Service)

**优先级**: Priority 1 - 基础设施模块  
**状态**: 🔥 待开发  
**依赖**: manticore_search (已完成)

## 🎯 模块职责

向量化引擎是连接文本内容和语义搜索的核心桥梁，负责将所有文本内容转换为高质量的向量表示，为智能上下文检索提供基础能力。

### 核心功能
- **文本向量化**: 支持多种embedding模型的文本向量化
- **向量缓存**: 智能缓存机制，避免重复计算
- **批量处理**: 高效的批量向量化处理
- **模型管理**: 支持多种embedding模型的切换和管理
- **相似度计算**: 提供向量相似度计算能力

## 🏗️ 技术架构

### 技术栈
- **Python 3.9+**: 主要开发语言
- **sentence-transformers**: 本地embedding模型
- **OpenAI API**: 云端embedding服务
- **Redis**: 向量缓存存储
- **FastAPI**: API服务框架
- **numpy**: 向量计算

### 架构设计
```
┌─────────────────────────────────────────────────────────────┐
│                    FastAPI Application                      │
├─────────────────────────────────────────────────────────────┤
│                      API Layer                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │  Embedding  │ │   Batch     │ │  Similarity │          │
│  │   Routes    │ │  Routes     │ │   Routes    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                    Service Layer                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │  Embedding  │ │   Cache     │ │   Model     │          │
│  │  Service    │ │  Service    │ │  Manager    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                     Model Layer                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ Local Model │ │ OpenAI API  │ │ Custom Model│          │
│  │(sentence-t) │ │  Embedding  │ │  Interface  │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                    Storage Layer                           │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                Redis Cache                             │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 📁 目录结构

```
embedding_service/
├── __init__.py              # 模块入口和导出
├── api/                     # API 层
│   ├── __init__.py
│   └── main.py             # FastAPI 应用和路由
├── services/               # 业务逻辑层
│   ├── __init__.py
│   ├── embedding_service.py # 向量化服务
│   ├── cache_service.py    # 缓存服务
│   └── model_manager.py    # 模型管理服务
├── models/                 # 数据模型层
│   ├── __init__.py
│   ├── embedding.py        # 向量化模型
│   └── response.py         # 响应模型
├── utils/                  # 工具模块
│   ├── __init__.py
│   ├── config.py          # 配置管理
│   ├── logger.py          # 日志系统
│   └── exceptions.py      # 异常定义
├── tests/                  # 测试模块
│   ├── __init__.py
│   ├── test_embedding_service.py
│   ├── test_cache_service.py
│   └── test_model_manager.py
├── docs/                   # 文档目录
│   ├── api-documentation.md
│   └── development-guide.md
├── requirements.txt        # 依赖包列表
├── docker-compose.yml      # 服务编排
├── Dockerfile             # 容器构建
├── test_module.py         # 模块测试脚本
└── README.md              # 模块说明 (本文件)
```

## 🚀 快速开始

### 环境要求
- Python 3.9+
- Redis (用于缓存)
- 2GB+ 内存 (用于本地模型)

### 1. 安装依赖
```bash
cd embedding_service
source ../.venv/bin/activate
pip install -r requirements.txt
```

### 2. 启动Redis缓存
```bash
# 使用Docker启动Redis
docker run -d --name redis-cache -p 6379:6379 redis:alpine
```

### 3. 配置环境变量
```bash
# 复制配置模板
cp .env.example .env

# 编辑配置文件
# EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
# OPENAI_API_KEY=your_openai_key_here
# REDIS_URL=redis://localhost:6379
```

### 4. 运行测试
```bash
# 运行模块测试
python test_module.py

# 运行单元测试
pytest tests/
```

### 5. 启动服务
```bash
# 开发模式启动
uvicorn api.main:app --reload --port 8001

# 生产模式启动
uvicorn api.main:app --host 0.0.0.0 --port 8001
```

## 💻 使用示例

### 基础导入
```python
from embedding_service import (
    EmbeddingService,
    EmbeddingRequest,
    BatchEmbeddingRequest,
    SimilarityRequest,
    get_settings
)
```

### 单文本向量化
```python
# 初始化服务
settings = get_settings()
embedding_service = EmbeddingService(settings)

# 创建向量化请求
request = EmbeddingRequest(
    text="Python是一种高级编程语言",
    model="sentence-transformers"  # 可选，使用默认模型
)

# 获取向量
response = embedding_service.embed_text(request)
print(f"向量维度: {len(response.embedding)}")
print(f"处理时间: {response.processing_time}ms")
```

### 批量向量化
```python
# 批量处理文本
texts = [
    "机器学习是人工智能的一个分支",
    "深度学习基于神经网络",
    "自然语言处理处理文本数据"
]

batch_request = BatchEmbeddingRequest(
    texts=texts,
    model="sentence-transformers"
)

# 批量向量化
batch_response = embedding_service.embed_batch(batch_request)
for i, embedding in enumerate(batch_response.embeddings):
    print(f"文本 {i+1} 向量维度: {len(embedding)}")
```

### 相似度计算
```python
# 计算两个向量的相似度
similarity_request = SimilarityRequest(
    vector1=response.embedding,
    vector2=batch_response.embeddings[0]
)

similarity = embedding_service.calculate_similarity(similarity_request)
print(f"相似度: {similarity.score:.4f}")
```

## 🔗 与其他模块集成

### 与manticore_search集成
```python
from manticore_search import DocumentService, DocumentCreate
from embedding_service import EmbeddingService

# 创建文档时自动生成向量
def create_document_with_embedding(title: str, content: str):
    # 生成向量
    embedding_request = EmbeddingRequest(text=content)
    embedding_response = embedding_service.embed_text(embedding_request)
    
    # 创建文档
    document = DocumentCreate(
        title=title,
        content=content,
        embedding=embedding_response.embedding
    )
    
    return doc_service.create_document(document)
```

### API接口设计
```python
# 向量化接口
POST /api/v1/embed
{
    "text": "要向量化的文本",
    "model": "sentence-transformers"  # 可选
}

# 批量向量化接口
POST /api/v1/embed/batch
{
    "texts": ["文本1", "文本2", "文本3"],
    "model": "sentence-transformers"
}

# 相似度计算接口
POST /api/v1/similarity
{
    "vector1": [0.1, 0.2, ...],
    "vector2": [0.3, 0.4, ...]
}

# 健康检查
GET /api/v1/health
```

## 🧪 测试策略

### 单元测试
- 向量化服务功能测试
- 缓存服务测试
- 模型管理测试
- 相似度计算测试

### 集成测试
- 与manticore_search集成测试
- Redis缓存集成测试
- API接口测试

### 性能测试
- 单文本向量化性能
- 批量处理性能
- 缓存命中率测试
- 内存使用监控

## 📊 性能指标

### 目标性能
- **单文本向量化**: <100ms
- **批量处理**: <50ms/文本 (批量优势)
- **缓存命中**: >80% (重复文本)
- **内存使用**: <1GB (包含模型)

### 优化策略
- 智能缓存机制
- 批量处理优化
- 模型预加载
- 异步处理支持

## 🔧 配置说明

### 环境变量
```bash
# 模型配置
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
EMBEDDING_DIMENSION=384
MODEL_CACHE_DIR=./models

# OpenAI配置 (可选)
OPENAI_API_KEY=your_key_here
OPENAI_MODEL=text-embedding-ada-002

# Redis配置
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600  # 缓存过期时间(秒)

# API配置
API_HOST=0.0.0.0
API_PORT=8001
```

## 🚧 开发计划

### Phase 1: 核心功能 (Week 1)
- [x] 项目结构搭建
- [ ] 基础向量化服务
- [ ] 本地模型集成
- [ ] 基础API接口

### Phase 2: 优化功能 (Week 2)
- [ ] Redis缓存集成
- [ ] 批量处理优化
- [ ] OpenAI API集成
- [ ] 性能监控

### Phase 3: 高级功能 (后续)
- [ ] 多模型支持
- [ ] 自定义模型接口
- [ ] 分布式部署
- [ ] 高级缓存策略

---

**模块版本**: 1.0.0  
**创建日期**: 2025-08-13  
**维护者**: Winston (Architect)
