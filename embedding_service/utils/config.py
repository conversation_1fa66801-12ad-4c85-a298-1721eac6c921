"""
向量化服务配置管理

基于全局配置模板的向量化服务专用配置
"""

from pydantic import Field, validator
from pydantic_settings import BaseSettings
from typing import Optional
from functools import lru_cache
import sys
import os

# 添加项目根目录到路径，以便导入全局配置模板
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))
from scripts.config.config_template import BaseServiceSettings, ExternalServiceMixin, RedisMixin


class EmbeddingServiceSettings(BaseServiceSettings, ExternalServiceMixin, RedisMixin):
    """向量化服务配置类"""

    # ===== 服务特定配置 =====
    api_port: int = Field(
        default=9001,
        description="向量化服务端口"
    )
    api_title: str = Field(
        default="Embedding Service API",
        description="向量化服务API标题"
    )
    api_description: str = Field(
        default="文本向量化和相似度计算服务",
        description="向量化服务API描述"
    )

    # ===== OpenAI API 配置 =====
    openai_api_key: str = Field(
        default="",
        description="OpenAI API密钥"
    )
    openai_base_url: str = Field(
        default="https://api.openai.com/v1",
        description="OpenAI API基础URL"
    )
    default_model: str = Field(
        default="text-embedding-ada-002",
        description="默认向量化模型"
    )

    # ===== 缓存配置 =====
    cache_ttl: int = Field(
        default=3600,  # 1小时
        ge=300, le=86400,  # 5分钟到24小时
        description="缓存过期时间(秒)"
    )
    enable_cache: bool = Field(
        default=False,  # MVP阶段暂时关闭
        description="是否启用缓存"
    )

    @validator('openai_api_key')
    def validate_openai_key(cls, v):
        if not v:
            import warnings
            warnings.warn("OpenAI API密钥未设置，服务可能无法正常工作", UserWarning)
        return v

    @validator('openai_base_url')
    def validate_openai_url(cls, v):
        if not v.startswith(('http://', 'https://')):
            raise ValueError("OpenAI API URL必须以http://或https://开头")
        return v.rstrip('/')

    class Config:
        env_prefix = "EMBEDDING_"
        env_file = ".env"
        case_sensitive = False


# 为了向后兼容，保留Settings别名
Settings = EmbeddingServiceSettings

@lru_cache()
def get_settings() -> EmbeddingServiceSettings:
    """获取向量化服务配置实例（单例模式）"""
    from scripts.config.config_template import get_service_settings
    return get_service_settings(EmbeddingServiceSettings)


if __name__ == "__main__":
    # 测试配置加载
    try:
        settings = get_settings()
        print("✅ 向量化服务配置加载成功")
        print(f"API端口: {settings.api_port}")
        print(f"OpenAI模型: {settings.default_model}")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
