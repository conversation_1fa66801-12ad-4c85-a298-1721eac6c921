from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # OpenAI API 配置
    openai_api_key: str = ""
    openai_base_url: str = "https://api.openai.com/v1"
    default_model: str = "text-embedding-ada-002"

    # 请求配置
    request_timeout: int = 30
    max_retries: int = 3

    # Redis缓存配置 (可选)
    redis_url: Optional[str] = None
    cache_ttl: int = 3600
    enable_cache: bool = False

    # API服务配置
    api_host: str = "0.0.0.0"
    api_port: int = 8001

    # 日志配置
    log_level: str = "INFO"

    class Config:
        env_prefix = "EMBEDDING_"
        env_file = ".env"

def get_settings() -> Settings:
    return Settings()
