import logging
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi import FastAPI, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from services.embedding_service import EmbeddingService
from models.embedding import (
    EmbeddingRequest,
    EmbeddingResponse,
    LegacyEmbeddingRequest,
    LegacyEmbeddingResponse
)
from utils.config import get_settings, Settings

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Embedding Service",
    version="2.0.0",
    description="OpenAI compatible embedding service"
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局服务实例 (避免重复初始化)
_embedding_service = None

async def get_embedding_service() -> EmbeddingService:
    global _embedding_service
    if _embedding_service is None:
        settings = get_settings()
        _embedding_service = EmbeddingService(settings)
    return _embedding_service

@app.post("/api/v1/embeddings", response_model=EmbeddingResponse)
async def create_embeddings(
    request: EmbeddingRequest,
    service: EmbeddingService = Depends(get_embedding_service)
):
    """
    Create embeddings for input text(s) - OpenAI compatible endpoint
    """
    try:
        logger.info(f"Received embedding request for model: {request.model}")
        response = await service.create_embeddings(request)
        logger.info(f"Successfully created {len(response.data)} embeddings")
        return response
    except Exception as e:
        logger.error(f"Error in create_embeddings: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# 向后兼容的端点 (已弃用)
@app.post("/api/v1/embed", response_model=LegacyEmbeddingResponse, deprecated=True)
async def embed_text_legacy(
    request: LegacyEmbeddingRequest,
    service: EmbeddingService = Depends(get_embedding_service)
):
    """
    Legacy embedding endpoint (deprecated) - use /api/v1/embeddings instead
    """
    try:
        logger.warning("Using deprecated /api/v1/embed endpoint")
        return await service.embed_text_legacy(request)
    except Exception as e:
        logger.error(f"Error in embed_text_legacy: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/health")
async def health_check():
    """
    Health check endpoint
    """
    try:
        settings = get_settings()
        return {
            "status": "healthy",
            "service": "embedding_service",
            "version": "2.0.0",
            "base_url": settings.openai_base_url,
            "default_model": settings.default_model
        }
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "service": "embedding_service",
            "error": str(e)
        }

@app.get("/api/v1/models")
async def list_models():
    """
    List available models (placeholder)
    """
    settings = get_settings()
    return {
        "object": "list",
        "data": [
            {
                "id": settings.default_model,
                "object": "model",
                "created": **********,
                "owned_by": "openai"
            }
        ]
    }
