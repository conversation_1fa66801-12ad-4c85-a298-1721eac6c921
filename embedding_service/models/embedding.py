from pydantic import BaseModel, Field
from typing import List, Optional, Union
from enum import Enum

class EncodingFormat(str, Enum):
    """Supported encoding formats for embeddings"""
    FLOAT = "float"
    BASE64 = "base64"

class EmbeddingRequest(BaseModel):
    """OpenAI compatible embedding request model"""
    input: Union[str, List[str]] = Field(..., description="Input text(s) to embed")
    model: str = Field(default="text-embedding-ada-002", description="Model to use for embedding")
    encoding_format: Optional[EncodingFormat] = Field(default=EncodingFormat.FLOAT, description="Format for embeddings")
    dimensions: Optional[int] = Field(default=None, description="Number of dimensions for embedding")
    user: Optional[str] = Field(default=None, description="User identifier")

class EmbeddingObject(BaseModel):
    """Individual embedding object"""
    object: str = Field(default="embedding", description="Object type")
    embedding: List[float] = Field(..., description="The embedding vector")
    index: int = Field(..., description="Index of the input")

class Usage(BaseModel):
    """Token usage information"""
    prompt_tokens: int = Field(..., description="Number of tokens in the prompt")
    total_tokens: int = Field(..., description="Total number of tokens used")

class EmbeddingResponse(BaseModel):
    """OpenAI compatible embedding response model"""
    object: str = Field(default="list", description="Object type")
    data: List[EmbeddingObject] = Field(..., description="List of embedding objects")
    model: str = Field(..., description="Model used for embedding")
    usage: Usage = Field(..., description="Token usage information")

# Legacy models for backward compatibility (deprecated)
class LegacyEmbeddingRequest(BaseModel):
    text: str
    model: Optional[str] = None

class LegacyEmbeddingResponse(BaseModel):
    embedding: List[float]
    dimension: int
    processing_time: float
    model: str
