# Embedding Service Configuration

# OpenAI API Configuration
EMBEDDING_OPENAI_API_KEY=your_openai_api_key_here
EMBEDDING_OPENAI_BASE_URL=https://api.openai.com/v1
EMBEDDING_DEFAULT_MODEL=text-embedding-ada-002

# Request Configuration
EMBEDDING_REQUEST_TIMEOUT=30
EMBEDDING_MAX_RETRIES=3

# Redis Cache Configuration (Optional)
EMBEDDING_REDIS_URL=redis://localhost:6379
EMBEDDING_CACHE_TTL=3600
EMBEDDING_ENABLE_CACHE=false

# API Server Configuration
EMBEDDING_API_HOST=0.0.0.0
EMBEDDING_API_PORT=8001

# Logging Configuration
EMBEDDING_LOG_LEVEL=INFO

# Example for local development with OpenAI:
# EMBEDDING_OPENAI_API_KEY=sk-your-actual-openai-key-here
# EMBEDDING_OPENAI_BASE_URL=https://api.openai.com/v1
# EMBEDDING_DEFAULT_MODEL=text-embedding-ada-002

# Example for local development with OpenAI-compatible service:
# EMBEDDING_OPENAI_API_KEY=your-local-api-key
# EMBEDDING_OPENAI_BASE_URL=http://localhost:8080/v1
# EMBEDDING_DEFAULT_MODEL=text-embedding-ada-002
