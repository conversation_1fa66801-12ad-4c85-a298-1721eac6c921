#!/usr/bin/env python3
"""
Embedding Service Usage Examples
演示如何使用升级后的 embedding_service
"""

import asyncio
import json
import httpx
from typing import List

# API 基础配置
BASE_URL = "http://localhost:8001"
API_ENDPOINT = f"{BASE_URL}/api/v1/embeddings"

async def test_single_text_embedding():
    """测试单文本向量化"""
    print("🧪 测试单文本向量化...")
    
    request_data = {
        "input": "Hello, world! This is a test sentence.",
        "model": "text-embedding-ada-002"
    }
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                API_ENDPOINT,
                json=request_data,
                timeout=30.0
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 成功获取向量")
                print(f"   - 向量维度: {len(result['data'][0]['embedding'])}")
                print(f"   - 使用模型: {result['model']}")
                print(f"   - Token使用: {result['usage']['total_tokens']}")
                return True
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"   错误信息: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")
            return False

async def test_batch_text_embedding():
    """测试批量文本向量化"""
    print("\n🧪 测试批量文本向量化...")
    
    request_data = {
        "input": [
            "This is the first sentence.",
            "This is the second sentence.",
            "This is the third sentence."
        ],
        "model": "text-embedding-ada-002"
    }
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                API_ENDPOINT,
                json=request_data,
                timeout=30.0
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 成功获取批量向量")
                print(f"   - 处理文本数: {len(result['data'])}")
                print(f"   - 向量维度: {len(result['data'][0]['embedding'])}")
                print(f"   - 使用模型: {result['model']}")
                print(f"   - Token使用: {result['usage']['total_tokens']}")
                
                # 显示每个向量的索引
                for i, embedding_obj in enumerate(result['data']):
                    print(f"   - 文本 {i}: 索引 {embedding_obj['index']}")
                
                return True
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"   错误信息: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")
            return False

async def test_health_check():
    """测试健康检查"""
    print("\n🧪 测试健康检查...")
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(f"{BASE_URL}/api/v1/health")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 服务健康")
                print(f"   - 状态: {result['status']}")
                print(f"   - 版本: {result['version']}")
                print(f"   - Base URL: {result['base_url']}")
                print(f"   - 默认模型: {result['default_model']}")
                return True
            else:
                print(f"❌ 健康检查失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 健康检查异常: {str(e)}")
            return False

async def test_models_endpoint():
    """测试模型列表端点"""
    print("\n🧪 测试模型列表...")
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(f"{BASE_URL}/api/v1/models")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 获取模型列表成功")
                print(f"   - 可用模型数: {len(result['data'])}")
                for model in result['data']:
                    print(f"   - 模型ID: {model['id']}")
                return True
            else:
                print(f"❌ 获取模型列表失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 获取模型列表异常: {str(e)}")
            return False

def print_curl_examples():
    """打印 curl 使用示例"""
    print("\n📋 Curl 使用示例:")
    print("\n1. 单文本向量化:")
    print(f"""curl -X POST {API_ENDPOINT} \\
  -H "Content-Type: application/json" \\
  -d '{{"input": "Hello, world!", "model": "text-embedding-ada-002"}}'""")
    
    print("\n2. 批量文本向量化:")
    print(f"""curl -X POST {API_ENDPOINT} \\
  -H "Content-Type: application/json" \\
  -d '{{"input": ["Hello", "World"], "model": "text-embedding-ada-002"}}'""")
    
    print("\n3. 健康检查:")
    print(f"curl {BASE_URL}/api/v1/health")
    
    print("\n4. 模型列表:")
    print(f"curl {BASE_URL}/api/v1/models")

async def main():
    """主测试函数"""
    print("=" * 60)
    print("    Embedding Service v2.0 - 使用示例")
    print("=" * 60)
    
    # 检查服务是否运行
    health_ok = await test_health_check()
    if not health_ok:
        print("\n❌ 服务未运行或配置错误")
        print("请确保:")
        print("1. 服务已启动: uvicorn api.main:app --reload --port 8001")
        print("2. 环境变量已设置: EMBEDDING_OPENAI_API_KEY")
        return
    
    # 运行测试
    await test_models_endpoint()
    
    single_ok = await test_single_text_embedding()
    batch_ok = await test_batch_text_embedding()
    
    print("\n" + "=" * 60)
    if single_ok and batch_ok:
        print("🎉 所有测试通过！服务运行正常")
    else:
        print("❌ 部分测试失败，请检查配置")
    
    print_curl_examples()

if __name__ == "__main__":
    asyncio.run(main())
