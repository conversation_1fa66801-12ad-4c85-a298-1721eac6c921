import asyncio
import logging
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from typing import List, Union, Optional
import httpx
from openai import AsyncOpenAI
from models.embedding import (
    EmbeddingRequest,
    EmbeddingResponse,
    EmbeddingObject,
    Usage,
    LegacyEmbeddingRequest,
    LegacyEmbeddingResponse
)
from utils.config import Settings

logger = logging.getLogger(__name__)

class EmbeddingService:
    def __init__(self, settings: Settings):
        self.settings = settings

        # 初始化 OpenAI 客户端
        self.client = AsyncOpenAI(
            api_key=settings.openai_api_key,
            base_url=settings.openai_base_url,
            timeout=settings.request_timeout,
            max_retries=settings.max_retries
        )

        logger.info(f"EmbeddingService initialized with base_url: {settings.openai_base_url}")

    async def create_embeddings(self, request: EmbeddingRequest) -> EmbeddingResponse:
        """
        Create embeddings using OpenAI compatible API
        """
        try:
            # 确保 input 是列表格式
            if isinstance(request.input, str):
                input_texts = [request.input]
            else:
                input_texts = request.input

            logger.info(f"Creating embeddings for {len(input_texts)} texts using model {request.model}")

            # 调用 OpenAI API
            response = await self.client.embeddings.create(
                input=input_texts,
                model=request.model,
                encoding_format=request.encoding_format.value if request.encoding_format else "float",
                dimensions=request.dimensions,
                user=request.user
            )

            # 转换为我们的响应格式
            embedding_objects = []
            for i, embedding_data in enumerate(response.data):
                embedding_objects.append(EmbeddingObject(
                    embedding=embedding_data.embedding,
                    index=i
                ))

            usage = Usage(
                prompt_tokens=response.usage.prompt_tokens,
                total_tokens=response.usage.total_tokens
            )

            return EmbeddingResponse(
                data=embedding_objects,
                model=response.model,
                usage=usage
            )

        except Exception as e:
            logger.error(f"Error creating embeddings: {str(e)}")
            raise

    async def embed_single_text(self, text: str, model: Optional[str] = None) -> List[float]:
        """
        便捷方法：为单个文本生成向量
        """
        request = EmbeddingRequest(
            input=text,
            model=model or self.settings.default_model
        )

        response = await self.create_embeddings(request)
        return response.data[0].embedding

    async def embed_batch_texts(self, texts: List[str], model: Optional[str] = None) -> List[List[float]]:
        """
        便捷方法：为多个文本生成向量
        """
        request = EmbeddingRequest(
            input=texts,
            model=model or self.settings.default_model
        )

        response = await self.create_embeddings(request)
        return [obj.embedding for obj in response.data]

    # 向后兼容的方法 (已弃用)
    async def embed_text_legacy(self, request: LegacyEmbeddingRequest) -> LegacyEmbeddingResponse:
        """
        向后兼容的单文本向量化方法 (已弃用)
        """
        logger.warning("Using deprecated embed_text_legacy method")

        embedding = await self.embed_single_text(
            text=request.text,
            model=request.model
        )

        return LegacyEmbeddingResponse(
            embedding=embedding,
            dimension=len(embedding),
            processing_time=0.0,  # 不再计算处理时间
            model=request.model or self.settings.default_model
        )
