"""
向量化引擎 (Embedding Service)

高内聚的向量化处理模块，提供文本向量化、缓存管理和相似度计算能力。
为知深学习导师项目提供核心的语义理解基础设施。

主要功能:
- 文本向量化 (支持多种embedding模型)
- 智能缓存机制 (Redis缓存)
- 批量处理能力 (性能优化)
- 相似度计算 (向量相似度)
- 模型管理 (多模型支持)

使用示例:
    from embedding_service import EmbeddingService, EmbeddingRequest
    
    service = EmbeddingService()
    request = EmbeddingRequest(text="Hello, world!")
    response = service.embed_text(request)
    print(f"向量维度: {len(response.embedding)}")
"""

__version__ = "1.0.0"
__author__ = "<PERSON> (Architect)"
__email__ = "<EMAIL>"

# 核心服务导出
from .services.embedding_service import EmbeddingService
from .services.cache_service import CacheService
from .services.model_manager import ModelManager

# 数据模型导出
from .models.embedding import (
    EmbeddingRequest,
    BatchEmbeddingRequest,
    EmbeddingResponse,
    BatchEmbeddingResponse,
    SimilarityRequest,
    SimilarityResponse
)

from .models.response import (
    HealthResponse,
    ErrorResponse,
    SuccessResponse
)

# 工具函数导出
from .utils.config import get_settings, Settings
from .utils.exceptions import (
    EmbeddingError,
    ModelNotFoundError,
    CacheError,
    ValidationError
)

# API应用导出 (用于服务启动)
from .api.main import app

__all__ = [
    # 版本信息
    "__version__",
    "__author__",
    "__email__",
    
    # 核心服务
    "EmbeddingService",
    "CacheService", 
    "ModelManager",
    
    # 数据模型
    "EmbeddingRequest",
    "BatchEmbeddingRequest",
    "EmbeddingResponse",
    "BatchEmbeddingResponse",
    "SimilarityRequest",
    "SimilarityResponse",
    "HealthResponse",
    "ErrorResponse",
    "SuccessResponse",
    
    # 配置和工具
    "get_settings",
    "Settings",
    
    # 异常类
    "EmbeddingError",
    "ModelNotFoundError",
    "CacheError",
    "ValidationError",
    
    # FastAPI应用
    "app"
]

# 模块初始化日志
import logging
logger = logging.getLogger(__name__)
logger.info(f"Embedding Service v{__version__} initialized")
