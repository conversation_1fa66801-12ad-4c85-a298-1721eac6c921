#!/usr/bin/env python3
import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_structure():
    print("🧪 测试embedding_service基础结构...")

    # 测试配置
    try:
        from utils.config import get_settings
        settings = get_settings()
        print(f"✅ 配置加载成功")
        print(f"   - Base URL: {settings.openai_base_url}")
        print(f"   - Default Model: {settings.default_model}")
        print(f"   - API Key: {'***' if settings.openai_api_key else 'Not Set'}")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False

    # 测试新的数据模型
    try:
        from models.embedding import EmbeddingRequest, EmbeddingResponse, EmbeddingObject, Usage

        # 测试单文本请求
        request = EmbeddingRequest(input="Hello, world!")
        print(f"✅ 单文本请求模型正常: {request.input}")

        # 测试批量请求
        batch_request = EmbeddingRequest(input=["Hello", "World"])
        print(f"✅ 批量请求模型正常: {len(batch_request.input)} texts")

        # 测试响应模型
        embedding_obj = EmbeddingObject(embedding=[0.1, 0.2, 0.3], index=0)
        usage = Usage(prompt_tokens=10, total_tokens=10)
        response = EmbeddingResponse(
            data=[embedding_obj],
            model="test-model",
            usage=usage
        )
        print(f"✅ 响应模型正常: {len(response.data)} embeddings")

    except Exception as e:
        print(f"❌ 数据模型失败: {e}")
        return False

    # 测试API结构
    try:
        from api.main import app
        print("✅ API应用创建成功")
        print(f"   - Title: {app.title}")
        print(f"   - Version: {app.version}")
    except Exception as e:
        print(f"❌ API应用失败: {e}")
        return False

    print("✅ embedding_service 基础结构测试通过")
    return True

def test_mock_openai_service():
    print("🧪 测试模拟OpenAI服务...")

    class MockOpenAIResponse:
        def __init__(self, texts):
            self.data = []
            for i, text in enumerate(texts):
                embedding_data = type('obj', (object,), {
                    'embedding': [0.1 * (i + 1)] * 1536,  # 模拟1536维向量
                    'index': i
                })
                self.data.append(embedding_data)

            self.model = "text-embedding-ada-002"
            self.usage = type('obj', (object,), {
                'prompt_tokens': len(' '.join(texts).split()),
                'total_tokens': len(' '.join(texts).split())
            })

    class MockEmbeddingService:
        async def create_embeddings(self, request):
            from models.embedding import EmbeddingResponse, EmbeddingObject, Usage

            # 模拟处理
            if isinstance(request.input, str):
                texts = [request.input]
            else:
                texts = request.input

            mock_response = MockOpenAIResponse(texts)

            # 转换为我们的格式
            embedding_objects = []
            for data in mock_response.data:
                embedding_objects.append(EmbeddingObject(
                    embedding=data.embedding,
                    index=data.index
                ))

            usage = Usage(
                prompt_tokens=mock_response.usage.prompt_tokens,
                total_tokens=mock_response.usage.total_tokens
            )

            return EmbeddingResponse(
                data=embedding_objects,
                model=mock_response.model,
                usage=usage
            )

    async def run_test():
        from models.embedding import EmbeddingRequest

        service = MockEmbeddingService()

        # 测试单文本
        request = EmbeddingRequest(input="Hello, world!")
        response = await service.create_embeddings(request)

        print(f"✅ 单文本向量维度: {len(response.data[0].embedding)}")
        print(f"✅ Token使用: {response.usage.total_tokens}")
        print(f"✅ 模型: {response.model}")

        # 测试批量文本
        batch_request = EmbeddingRequest(input=["Hello", "World", "Test"])
        batch_response = await service.create_embeddings(batch_request)

        print(f"✅ 批量处理: {len(batch_response.data)} embeddings")
        print("✅ 模拟OpenAI服务测试通过")

    asyncio.run(run_test())

def test_environment_setup():
    print("🧪 测试环境配置...")

    # 检查环境变量
    api_key = os.getenv('EMBEDDING_OPENAI_API_KEY', '')
    base_url = os.getenv('EMBEDDING_OPENAI_BASE_URL', 'https://api.openai.com/v1')

    print(f"✅ API Key: {'设置' if api_key else '未设置 (需要设置EMBEDDING_OPENAI_API_KEY)'}")
    print(f"✅ Base URL: {base_url}")

    if not api_key:
        print("⚠️  警告: 未设置API密钥，实际调用将失败")
        print("   请设置环境变量: export EMBEDDING_OPENAI_API_KEY=your_key_here")

    return True

if __name__ == "__main__":
    print("=" * 60)
    print("    Embedding Service v2.0 - OpenAI Compatible")
    print("=" * 60)

    success = True

    if test_basic_structure():
        test_mock_openai_service()
        test_environment_setup()
        print("\n🎉 所有测试通过！")
        print("\n📋 下一步操作:")
        print("1. 设置环境变量:")
        print("   export EMBEDDING_OPENAI_API_KEY=your_key_here")
        print("   export EMBEDDING_OPENAI_BASE_URL=https://api.openai.com/v1  # 可选")
        print("\n2. 安装依赖:")
        print("   pip install -r requirements.txt")
        print("\n3. 启动服务:")
        print("   uvicorn api.main:app --reload --port 8001")
        print("\n4. 测试API:")
        print("   curl -X POST http://localhost:8001/api/v1/embeddings \\")
        print("        -H 'Content-Type: application/json' \\")
        print("        -d '{\"input\": \"Hello, world!\", \"model\": \"text-embedding-ada-002\"}'")
    else:
        print("\n❌ 基础结构测试失败，请检查代码")
        success = False

    sys.exit(0 if success else 1)
