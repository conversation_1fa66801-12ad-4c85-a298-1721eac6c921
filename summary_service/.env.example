# Summary Service Configuration

# ===== API服务配置 =====
SUMMARY_API_HOST=0.0.0.0
SUMMARY_API_PORT=9008
SUMMARY_API_TITLE="Summary Service API"
SUMMARY_API_DESCRIPTION="智能摘要生成和回溯服务"
SUMMARY_API_VERSION=1.0.0
SUMMARY_API_PREFIX=/api/v1

# ===== 日志配置 =====
SUMMARY_LOG_LEVEL=INFO
SUMMARY_LOG_FORMAT=json
SUMMARY_ENABLE_REQUEST_LOGGING=true

# ===== 健康检查配置 =====
SUMMARY_HEALTH_CHECK_TIMEOUT=5
SUMMARY_ENABLE_HEALTH_CHECK=true

# ===== 安全配置 =====
SUMMARY_ENABLE_CORS=true
SUMMARY_CORS_ORIGINS=["*"]

# ===== 环境配置 =====
SUMMARY_ENVIRONMENT=development
SUMMARY_DEBUG=false

# ===== 外部服务配置 =====
SUMMARY_REQUEST_TIMEOUT=30
SUMMARY_MAX_RETRIES=3
SUMMARY_RETRY_DELAY=1.0

# ===== 服务特定配置 =====
# 数据库配置
SUMMARY_DATABASE_URL=postgresql://master_know_user:master_know_pass@localhost:5432/master_know
SUMMARY_DATABASE_POOL_SIZE=10
SUMMARY_DATABASE_POOL_TIMEOUT=30
SUMMARY_DATABASE_ECHO=false

# Redis配置
SUMMARY_REDIS_URL=redis://localhost:6379
SUMMARY_REDIS_POOL_SIZE=10
SUMMARY_REDIS_TIMEOUT=5
SUMMARY_ENABLE_REDIS=true

# 外部服务依赖
SUMMARY_LLM_INTEGRATION_URL=http://localhost:9006
SUMMARY_CONVERSATION_SERVICE_URL=http://localhost:9007
SUMMARY_DOCUMENT_SERVICE_URL=http://localhost:9005

# 摘要生成配置
SUMMARY_ENABLE_AUTO_SUMMARY=true
SUMMARY_SUMMARY_TRIGGER_THRESHOLD=10
SUMMARY_SUMMARY_MAX_LENGTH=500
SUMMARY_SUMMARY_MIN_LENGTH=50

# 摘要质量配置
SUMMARY_ENABLE_QUALITY_CHECK=true
SUMMARY_QUALITY_THRESHOLD=0.7
SUMMARY_ENABLE_REDUNDANCY_REMOVAL=true

# 回溯映射配置
SUMMARY_ENABLE_BACKTRACKING=true
SUMMARY_BACKTRACK_DEPTH=5
SUMMARY_ENABLE_SOURCE_LINKING=true

# 摘要类型配置
SUMMARY_SUPPORTED_SUMMARY_TYPES=["extractive", "abstractive", "hybrid"]
SUMMARY_DEFAULT_SUMMARY_TYPE=hybrid
SUMMARY_ENABLE_MULTI_LEVEL_SUMMARY=true

# 缓存配置
SUMMARY_ENABLE_SUMMARY_CACHE=true
SUMMARY_SUMMARY_CACHE_TTL=3600
SUMMARY_ENABLE_INCREMENTAL_UPDATE=true

# 批处理配置
SUMMARY_ENABLE_BATCH_PROCESSING=true
SUMMARY_BATCH_SIZE=10
SUMMARY_BATCH_TIMEOUT=300

# 优化配置
SUMMARY_ENABLE_SUMMARY_OPTIMIZATION=true
SUMMARY_OPTIMIZATION_ITERATIONS=3
SUMMARY_ENABLE_PERSONALIZATION=false

# 性能配置
SUMMARY_MAX_CONCURRENT_SUMMARIES=5
SUMMARY_SUMMARY_TIMEOUT=120
