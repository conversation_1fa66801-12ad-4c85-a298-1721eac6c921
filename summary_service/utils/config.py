"""
摘要服务配置管理

基于全局配置模板的摘要服务专用配置
"""

from pydantic import Field, validator
from typing import Optional, List
from functools import lru_cache
import sys
import os

# 添加项目根目录到路径，以便导入全局配置模板
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))
from scripts.config.config_template import BaseServiceSettings, DatabaseMixin, RedisMixin, ExternalServiceMixin


class SummaryServiceSettings(BaseServiceSettings, DatabaseMixin, RedisMixin, ExternalServiceMixin):
    """摘要服务配置类"""
    
    # ===== 服务特定配置 =====
    api_port: int = Field(
        default=9008,
        description="摘要服务端口"
    )
    api_title: str = Field(
        default="Summary Service API",
        description="摘要服务API标题"
    )
    api_description: str = Field(
        default="智能摘要生成和回溯服务",
        description="摘要服务API描述"
    )
    
    # ===== 外部服务依赖 =====
    llm_integration_url: str = Field(
        default="http://localhost:9006",
        description="LLM集成服务URL"
    )
    conversation_service_url: str = Field(
        default="http://localhost:9007",
        description="对话服务URL"
    )
    document_service_url: str = Field(
        default="http://localhost:9005",
        description="文档服务URL"
    )
    
    # ===== 摘要生成配置 =====
    enable_auto_summary: bool = Field(
        default=True,
        description="是否启用自动摘要"
    )
    summary_trigger_threshold: int = Field(
        default=10,
        ge=3, le=50,
        description="触发摘要的对话轮数阈值"
    )
    summary_max_length: int = Field(
        default=500,
        ge=100, le=2000,
        description="摘要最大长度"
    )
    summary_min_length: int = Field(
        default=50,
        ge=20, le=200,
        description="摘要最小长度"
    )
    
    # ===== 摘要质量配置 =====
    enable_quality_check: bool = Field(
        default=True,
        description="是否启用摘要质量检查"
    )
    quality_threshold: float = Field(
        default=0.7,
        ge=0.0, le=1.0,
        description="摘要质量阈值"
    )
    enable_redundancy_removal: bool = Field(
        default=True,
        description="是否启用冗余去除"
    )
    
    # ===== 回溯映射配置 =====
    enable_backtracking: bool = Field(
        default=True,
        description="是否启用回溯映射"
    )
    backtrack_depth: int = Field(
        default=5,
        ge=1, le=20,
        description="回溯深度"
    )
    enable_source_linking: bool = Field(
        default=True,
        description="是否启用源文档链接"
    )
    
    # ===== 摘要类型配置 =====
    supported_summary_types: List[str] = Field(
        default=["extractive", "abstractive", "hybrid"],
        description="支持的摘要类型"
    )
    default_summary_type: str = Field(
        default="hybrid",
        description="默认摘要类型"
    )
    enable_multi_level_summary: bool = Field(
        default=True,
        description="是否启用多级摘要"
    )
    
    # ===== 缓存配置 =====
    enable_summary_cache: bool = Field(
        default=True,
        description="是否启用摘要缓存"
    )
    summary_cache_ttl: int = Field(
        default=3600,  # 1小时
        ge=300, le=86400,
        description="摘要缓存过期时间(秒)"
    )
    enable_incremental_update: bool = Field(
        default=True,
        description="是否启用增量更新"
    )
    
    # ===== 批处理配置 =====
    enable_batch_processing: bool = Field(
        default=True,
        description="是否启用批处理"
    )
    batch_size: int = Field(
        default=10,
        ge=1, le=100,
        description="批处理大小"
    )
    batch_timeout: int = Field(
        default=300,  # 5分钟
        ge=60, le=1800,
        description="批处理超时时间(秒)"
    )
    
    # ===== 优化配置 =====
    enable_summary_optimization: bool = Field(
        default=True,
        description="是否启用摘要优化"
    )
    optimization_iterations: int = Field(
        default=3,
        ge=1, le=10,
        description="优化迭代次数"
    )
    enable_personalization: bool = Field(
        default=False,  # MVP阶段暂时关闭
        description="是否启用个性化摘要"
    )
    
    # ===== 性能配置 =====
    max_concurrent_summaries: int = Field(
        default=5,
        ge=1, le=20,
        description="最大并发摘要任务数"
    )
    summary_timeout: int = Field(
        default=120,  # 2分钟
        ge=30, le=600,
        description="单个摘要超时时间(秒)"
    )
    
    @validator('llm_integration_url', 'conversation_service_url', 'document_service_url')
    def validate_service_urls(cls, v):
        if not v.startswith(('http://', 'https://')):
            raise ValueError("服务URL必须以http://或https://开头")
        return v.rstrip('/')
    
    @validator('default_summary_type')
    def validate_summary_type(cls, v, values):
        if 'supported_summary_types' in values:
            if v not in values['supported_summary_types']:
                raise ValueError(f'默认摘要类型必须在支持的类型中: {values["supported_summary_types"]}')
        return v
    
    class Config:
        env_prefix = "SUMMARY_"
        env_file = ".env"
        case_sensitive = False


@lru_cache()
def get_settings() -> SummaryServiceSettings:
    """获取摘要服务配置实例（单例模式）"""
    from scripts.config.config_template import get_service_settings
    return get_service_settings(SummaryServiceSettings)


def create_env_example() -> str:
    """创建摘要服务的.env.example文件内容"""
    from scripts.config.config_template import create_env_template
    
    additional_vars = {
        # 数据库配置
        "DATABASE_URL": "postgresql://master_know_user:master_know_pass@localhost:5432/master_know",
        "DATABASE_POOL_SIZE": "10",
        "DATABASE_POOL_TIMEOUT": "30",
        "DATABASE_ECHO": "false",
        
        # Redis配置
        "REDIS_URL": "redis://localhost:6379",
        "REDIS_POOL_SIZE": "10",
        "REDIS_TIMEOUT": "5",
        "ENABLE_REDIS": "true",
        
        # 外部服务依赖
        "LLM_INTEGRATION_URL": "http://localhost:9006",
        "CONVERSATION_SERVICE_URL": "http://localhost:9007",
        "DOCUMENT_SERVICE_URL": "http://localhost:9005",
        
        # 摘要生成配置
        "ENABLE_AUTO_SUMMARY": "true",
        "SUMMARY_TRIGGER_THRESHOLD": "10",
        "SUMMARY_MAX_LENGTH": "500",
        "SUMMARY_MIN_LENGTH": "50",
        
        # 摘要质量配置
        "ENABLE_QUALITY_CHECK": "true",
        "QUALITY_THRESHOLD": "0.7",
        "ENABLE_REDUNDANCY_REMOVAL": "true",
        
        # 回溯映射配置
        "ENABLE_BACKTRACKING": "true",
        "BACKTRACK_DEPTH": "5",
        "ENABLE_SOURCE_LINKING": "true",
        
        # 摘要类型配置
        "SUPPORTED_SUMMARY_TYPES": '["extractive", "abstractive", "hybrid"]',
        "DEFAULT_SUMMARY_TYPE": "hybrid",
        "ENABLE_MULTI_LEVEL_SUMMARY": "true",
        
        # 缓存配置
        "ENABLE_SUMMARY_CACHE": "true",
        "SUMMARY_CACHE_TTL": "3600",
        "ENABLE_INCREMENTAL_UPDATE": "true",
        
        # 批处理配置
        "ENABLE_BATCH_PROCESSING": "true",
        "BATCH_SIZE": "10",
        "BATCH_TIMEOUT": "300",
        
        # 优化配置
        "ENABLE_SUMMARY_OPTIMIZATION": "true",
        "OPTIMIZATION_ITERATIONS": "3",
        "ENABLE_PERSONALIZATION": "false",
        
        # 性能配置
        "MAX_CONCURRENT_SUMMARIES": "5",
        "SUMMARY_TIMEOUT": "120",
    }
    
    return create_env_template("Summary Service", "SUMMARY", additional_vars)


if __name__ == "__main__":
    # 测试配置加载
    try:
        settings = get_settings()
        print("✅ 摘要服务配置加载成功")
        print(f"API端口: {settings.api_port}")
        print(f"自动摘要: {'启用' if settings.enable_auto_summary else '禁用'}")
        print(f"触发阈值: {settings.summary_trigger_threshold}轮对话")
        print(f"摘要类型: {settings.default_summary_type}")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
    
    # 生成.env.example文件
    env_content = create_env_example()
    with open("summary_service/.env.example", "w", encoding="utf-8") as f:
        f.write(env_content)
    print("✅ 已生成 summary_service/.env.example 文件")
