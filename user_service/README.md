# 用户服务 (User Service) - 简化版

**优先级**: Priority 2 - 基础设施模块
**状态**: 👤 待开发 (简化版)
**依赖**: 无

## 🎯 模块职责 (简化版)

用户服务的简化版本，专注于为MVP提供基础的用户信息支持，无需复杂的认证和权限管理。

### 核心功能 (简化版)
- **固定测试用户**: 创建一个写死的测试用户 (user_id=1, username="test_user")
- **用户信息接口**: 提供基础的用户信息查询接口
- **简单用户管理**: 基础的用户数据结构，为后续扩展预留接口
- **无认证模式**: 直接返回测试用户信息，无需登录流程

## 🏗️ 技术架构

### 技术栈 (简化版)
- **Python 3.9+**: 主要开发语言
- **FastAPI**: API服务框架
- **Pydantic**: 数据验证 (无需数据库存储)
- **内存存储**: 固定用户数据，无需数据库

### 架构设计
```
┌─────────────────────────────────────────────────────────────┐
│                    FastAPI Application                      │
├─────────────────────────────────────────────────────────────┤
│                      API Layer                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │    Auth     │ │    User     │ │  Session    │          │
│  │   Routes    │ │   Routes    │ │   Routes    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                    Service Layer                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │    Auth     │ │    User     │ │  Session    │          │
│  │  Service    │ │  Service    │ │  Service    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                    Storage Layer                           │
│  ┌─────────────┐ ┌─────────────────────────────────────────┐ │
│  │ PostgreSQL  │ │              Redis                     │ │
│  │ (用户数据)   │ │            (会话缓存)                   │ │
│  └─────────────┘ └─────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 📁 目录结构

```
user_service/
├── __init__.py              # 模块入口和导出
├── api/                     # API 层
│   ├── __init__.py
│   └── main.py             # FastAPI 应用和路由
├── services/               # 业务逻辑层
│   ├── __init__.py
│   ├── auth_service.py     # 认证服务
│   ├── user_service.py     # 用户管理服务
│   └── session_service.py  # 会话管理服务
├── models/                 # 数据模型层
│   ├── __init__.py
│   ├── user.py            # 用户模型
│   ├── auth.py            # 认证模型
│   └── session.py         # 会话模型
├── utils/                  # 工具模块
│   ├── __init__.py
│   ├── config.py          # 配置管理
│   ├── security.py        # 安全工具
│   ├── database.py        # 数据库连接
│   └── exceptions.py      # 异常定义
├── tests/                  # 测试模块
│   ├── __init__.py
│   ├── test_auth_service.py
│   ├── test_user_service.py
│   └── test_session_service.py
├── docs/                   # 文档目录
│   └── api-documentation.md
├── migrations/             # 数据库迁移
│   └── init_users.sql
├── requirements.txt        # 依赖包列表
├── docker-compose.yml      # 服务编排
├── test_module.py         # 模块测试脚本
└── README.md              # 模块说明 (本文件)
```

## 🚀 快速开始

### 环境要求
- Python 3.9+
- PostgreSQL 13+
- Redis 6+

### 1. 安装依赖
```bash
cd user_service
source ../.venv/bin/activate
pip install -r requirements.txt
```

### 2. 启动数据库服务
```bash
# 启动PostgreSQL和Redis
docker-compose up -d
```

### 3. 初始化数据库
```bash
# 运行数据库迁移
python -c "from utils.database import init_db; init_db()"
```

### 4. 配置环境变量
```bash
# 编辑配置文件
# DATABASE_URL=postgresql://user:pass@localhost:5432/master_know
# REDIS_URL=redis://localhost:6379
# JWT_SECRET_KEY=your_secret_key_here
# JWT_ALGORITHM=HS256
# JWT_EXPIRE_MINUTES=1440
```

### 5. 运行测试
```bash
python test_module.py
```

### 6. 启动服务
```bash
uvicorn api.main:app --reload --port 8002
```

## 💻 使用示例

### 用户注册
```python
from user_service import AuthService, UserCreate

auth_service = AuthService()

# 创建用户
user_data = UserCreate(
    username="john_doe",
    email="<EMAIL>",
    password="secure_password"
)

user = auth_service.register_user(user_data)
print(f"用户创建成功: {user.username}")
```

### 用户登录
```python
from user_service import LoginRequest

# 用户登录
login_data = LoginRequest(
    username="john_doe",
    password="secure_password"
)

token_response = auth_service.login(login_data)
print(f"访问令牌: {token_response.access_token}")
```

### 会话管理
```python
from user_service import SessionService

session_service = SessionService()

# 验证令牌
user = session_service.get_current_user(token_response.access_token)
print(f"当前用户: {user.username}")

# 刷新令牌
new_token = session_service.refresh_token(token_response.refresh_token)
```

## 🔗 API接口设计

### 认证接口
```python
# 用户注册
POST /api/v1/auth/register
{
    "username": "john_doe",
    "email": "<EMAIL>", 
    "password": "secure_password"
}

# 用户登录
POST /api/v1/auth/login
{
    "username": "john_doe",
    "password": "secure_password"
}

# 刷新令牌
POST /api/v1/auth/refresh
{
    "refresh_token": "refresh_token_here"
}

# 用户登出
POST /api/v1/auth/logout
# Headers: Authorization: Bearer <access_token>
```

### 用户管理接口
```python
# 获取用户信息
GET /api/v1/users/me
# Headers: Authorization: Bearer <access_token>

# 更新用户信息
PUT /api/v1/users/me
{
    "email": "<EMAIL>",
    "preferences": {"theme": "dark"}
}

# 修改密码
POST /api/v1/users/change-password
{
    "current_password": "old_password",
    "new_password": "new_password"
}
```

## 🔒 安全设计

### 密码安全
- 使用bcrypt进行密码哈希
- 强制密码复杂度要求
- 防止密码重用

### Token安全
- JWT Token签名验证
- Token过期时间控制
- Refresh Token轮换机制

### 会话安全
- Redis会话存储
- 会话超时管理
- 并发会话限制

## 🧪 测试策略

### 单元测试
- 认证服务测试
- 用户管理测试
- 会话管理测试
- 安全功能测试

### 集成测试
- 数据库集成测试
- Redis集成测试
- API接口测试

### 安全测试
- 认证绕过测试
- Token安全测试
- 密码安全测试

## 📊 性能指标

### 目标性能
- **登录响应**: <200ms
- **Token验证**: <50ms
- **并发用户**: 1000+
- **会话存储**: Redis高性能

## 🔧 配置说明

### 环境变量
```bash
# 数据库配置
DATABASE_URL=postgresql://user:pass@localhost:5432/master_know
REDIS_URL=redis://localhost:6379

# JWT配置
JWT_SECRET_KEY=your_secret_key_here
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=1440
JWT_REFRESH_EXPIRE_DAYS=30

# 安全配置
PASSWORD_MIN_LENGTH=8
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=300

# API配置
API_HOST=0.0.0.0
API_PORT=8002
```

---

**模块版本**: 1.0.0  
**创建日期**: 2025-08-13  
**维护者**: Winston (Architect)
