from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from ..services.user_service import UserService
from ..services.health_service import get_health_status, get_ready_status
from ..models.user import UserResponse, User
from ..utils.config import get_settings

# 获取配置
settings = get_settings()

app = FastAPI(
    title=settings.api_title,
    version=settings.api_version,
    description=settings.api_description
)

# 添加CORS中间件
if settings.enable_cors:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

user_service = UserService()

@app.get("/api/v1/users/me", response_model=UserResponse)
async def get_current_user():
    """获取当前用户（固定返回测试用户）"""
    return user_service.get_current_user()

@app.get("/api/v1/users/{user_id}", response_model=User)
async def get_user(user_id: int):
    """根据ID获取用户"""
    user = user_service.get_user_by_id(user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user

@app.get("/api/v1/health")
async def health_check():
    """标准健康检查接口"""
    return await get_health_status()

@app.get("/api/v1/ready")
async def readiness_check():
    """就绪检查接口（Kubernetes就绪探针）"""
    return await get_ready_status()
