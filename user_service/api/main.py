from fastapi import FastAPI, HTTPException
from ..services.user_service import UserService
from ..models.user import UserResponse, User

app = FastAPI(title="User Service (Simplified)", version="1.0.0")
user_service = UserService()

@app.get("/api/v1/users/me", response_model=UserResponse)
async def get_current_user():
    """获取当前用户（固定返回测试用户）"""
    return user_service.get_current_user()

@app.get("/api/v1/users/{user_id}", response_model=User)
async def get_user(user_id: int):
    """根据ID获取用户"""
    user = user_service.get_user_by_id(user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user

@app.get("/api/v1/health")
async def health_check():
    return {"status": "healthy", "service": "user_service"}
