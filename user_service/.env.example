# User Service Configuration

# ===== API服务配置 =====
USER_API_HOST=0.0.0.0
USER_API_PORT=9002
USER_API_TITLE="User Service API"
USER_API_DESCRIPTION="用户认证和管理服务"
USER_API_VERSION=1.0.0
USER_API_PREFIX=/api/v1

# ===== 日志配置 =====
USER_LOG_LEVEL=INFO
USER_LOG_FORMAT=json
USER_ENABLE_REQUEST_LOGGING=true

# ===== 健康检查配置 =====
USER_HEALTH_CHECK_TIMEOUT=5
USER_ENABLE_HEALTH_CHECK=true

# ===== 安全配置 =====
USER_ENABLE_CORS=true
USER_CORS_ORIGINS=["*"]

# ===== 环境配置 =====
USER_ENVIRONMENT=development
USER_DEBUG=false

# ===== 外部服务配置 =====
USER_REQUEST_TIMEOUT=30
USER_MAX_RETRIES=3
USER_RETRY_DELAY=1.0

# ===== 服务特定配置 =====
# JWT配置
USER_JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production-32-chars-minimum
USER_JWT_ALGORITHM=HS256
USER_JWT_EXPIRE_MINUTES=1440
USER_JWT_REFRESH_EXPIRE_DAYS=30

# 数据库配置
USER_DATABASE_URL=postgresql://master_know_user:master_know_pass@localhost:5432/master_know
USER_DATABASE_POOL_SIZE=10
USER_DATABASE_POOL_TIMEOUT=30
USER_DATABASE_ECHO=false

# Redis配置
USER_REDIS_URL=redis://localhost:6379
USER_REDIS_POOL_SIZE=10
USER_REDIS_TIMEOUT=5
USER_ENABLE_REDIS=true

# 密码安全配置
USER_PASSWORD_MIN_LENGTH=8
USER_PASSWORD_REQUIRE_UPPERCASE=true
USER_PASSWORD_REQUIRE_LOWERCASE=true
USER_PASSWORD_REQUIRE_NUMBERS=true
USER_PASSWORD_REQUIRE_SPECIAL=false

# 安全配置
USER_MAX_LOGIN_ATTEMPTS=5
USER_LOCKOUT_DURATION=300
USER_SESSION_TIMEOUT=3600
USER_MAX_CONCURRENT_SESSIONS=5

# 用户管理配置
USER_ENABLE_USER_REGISTRATION=true
USER_REQUIRE_EMAIL_VERIFICATION=false
USER_DEFAULT_USER_ROLE=user

# 邮件配置 (可选)
USER_SMTP_HOST=
USER_SMTP_PORT=587
USER_SMTP_USERNAME=
USER_SMTP_PASSWORD=
USER_SMTP_USE_TLS=true
