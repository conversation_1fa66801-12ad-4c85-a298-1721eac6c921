from datetime import datetime
from ..models.user import User, UserResponse

class UserService:
    def __init__(self):
        # 固定的测试用户
        self.test_user = User(
            id=1,
            username="test_user",
            email="<EMAIL>",
            created_at=datetime.now(),
            is_active=True
        )
    
    def get_current_user(self) -> UserResponse:
        """返回固定的测试用户"""
        return UserResponse(
            user=self.test_user,
            message="返回固定测试用户"
        )
    
    def get_user_by_id(self, user_id: int) -> User:
        """根据ID获取用户（简化版只支持ID=1）"""
        if user_id == 1:
            return self.test_user
        return None
