"""
用户服务配置管理

基于全局配置模板的用户服务专用配置
"""

from pydantic import Field, validator
from typing import Optional
from functools import lru_cache
import sys
import os

# 添加项目根目录到路径，以便导入全局配置模板
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))
from scripts.config.config_template import BaseServiceSettings, DatabaseMixin, RedisMixin


class UserServiceSettings(BaseServiceSettings, DatabaseMixin, RedisMixin):
    """用户服务配置类"""
    
    # ===== 服务特定配置 =====
    api_port: int = Field(
        default=9002,
        description="用户服务端口"
    )
    api_title: str = Field(
        default="User Service API",
        description="用户服务API标题"
    )
    api_description: str = Field(
        default="用户认证和管理服务",
        description="用户服务API描述"
    )
    
    # ===== JWT配置 =====
    jwt_secret_key: str = Field(
        default="your-secret-key-change-in-production",
        description="JWT签名密钥"
    )
    jwt_algorithm: str = Field(
        default="HS256",
        description="JWT签名算法"
    )
    jwt_expire_minutes: int = Field(
        default=1440,  # 24小时
        ge=1, le=10080,  # 最长7天
        description="JWT过期时间(分钟)"
    )
    jwt_refresh_expire_days: int = Field(
        default=30,
        ge=1, le=365,
        description="刷新Token过期时间(天)"
    )
    
    # ===== 密码安全配置 =====
    password_min_length: int = Field(
        default=8,
        ge=6, le=128,
        description="密码最小长度"
    )
    password_require_uppercase: bool = Field(
        default=True,
        description="密码是否需要大写字母"
    )
    password_require_lowercase: bool = Field(
        default=True,
        description="密码是否需要小写字母"
    )
    password_require_numbers: bool = Field(
        default=True,
        description="密码是否需要数字"
    )
    password_require_special: bool = Field(
        default=False,
        description="密码是否需要特殊字符"
    )
    
    # ===== 安全配置 =====
    max_login_attempts: int = Field(
        default=5,
        ge=3, le=20,
        description="最大登录尝试次数"
    )
    lockout_duration: int = Field(
        default=300,  # 5分钟
        ge=60, le=3600,  # 1分钟到1小时
        description="账户锁定时间(秒)"
    )
    session_timeout: int = Field(
        default=3600,  # 1小时
        ge=300, le=86400,  # 5分钟到24小时
        description="会话超时时间(秒)"
    )
    max_concurrent_sessions: int = Field(
        default=5,
        ge=1, le=20,
        description="最大并发会话数"
    )
    
    # ===== 用户管理配置 =====
    enable_user_registration: bool = Field(
        default=True,
        description="是否允许用户注册"
    )
    require_email_verification: bool = Field(
        default=False,
        description="是否需要邮箱验证"
    )
    default_user_role: str = Field(
        default="user",
        description="默认用户角色"
    )
    
    # ===== 邮件配置 (可选) =====
    smtp_host: Optional[str] = Field(
        default=None,
        description="SMTP服务器地址"
    )
    smtp_port: int = Field(
        default=587,
        description="SMTP服务器端口"
    )
    smtp_username: Optional[str] = Field(
        default=None,
        description="SMTP用户名"
    )
    smtp_password: Optional[str] = Field(
        default=None,
        description="SMTP密码"
    )
    smtp_use_tls: bool = Field(
        default=True,
        description="是否使用TLS"
    )
    
    @validator('jwt_secret_key')
    def validate_jwt_secret(cls, v):
        if v == "your-secret-key-change-in-production":
            import warnings
            warnings.warn("请在生产环境中更改JWT密钥！", UserWarning)
        if len(v) < 32:
            raise ValueError("JWT密钥长度至少32个字符")
        return v
    
    @validator('jwt_algorithm')
    def validate_jwt_algorithm(cls, v):
        valid_algorithms = ['HS256', 'HS384', 'HS512', 'RS256', 'RS384', 'RS512']
        if v not in valid_algorithms:
            raise ValueError(f'JWT算法必须是以下之一: {valid_algorithms}')
        return v
    
    @validator('default_user_role')
    def validate_default_role(cls, v):
        valid_roles = ['user', 'admin', 'guest']
        if v not in valid_roles:
            raise ValueError(f'默认用户角色必须是以下之一: {valid_roles}')
        return v
    
    class Config:
        env_prefix = "USER_"
        env_file = ".env"
        case_sensitive = False


@lru_cache()
def get_settings() -> UserServiceSettings:
    """获取用户服务配置实例（单例模式）"""
    from scripts.config.config_template import get_service_settings
    return get_service_settings(UserServiceSettings)


def create_env_example() -> str:
    """创建用户服务的.env.example文件内容"""
    from scripts.config.config_template import create_env_template
    
    additional_vars = {
        # JWT配置
        "JWT_SECRET_KEY": "your-super-secret-jwt-key-change-in-production-32-chars-minimum",
        "JWT_ALGORITHM": "HS256",
        "JWT_EXPIRE_MINUTES": "1440",
        "JWT_REFRESH_EXPIRE_DAYS": "30",
        
        # 数据库配置
        "DATABASE_URL": "postgresql://master_know_user:master_know_pass@localhost:5432/master_know",
        "DATABASE_POOL_SIZE": "10",
        "DATABASE_POOL_TIMEOUT": "30",
        "DATABASE_ECHO": "false",
        
        # Redis配置
        "REDIS_URL": "redis://localhost:6379",
        "REDIS_POOL_SIZE": "10",
        "REDIS_TIMEOUT": "5",
        "ENABLE_REDIS": "true",
        
        # 密码安全配置
        "PASSWORD_MIN_LENGTH": "8",
        "PASSWORD_REQUIRE_UPPERCASE": "true",
        "PASSWORD_REQUIRE_LOWERCASE": "true",
        "PASSWORD_REQUIRE_NUMBERS": "true",
        "PASSWORD_REQUIRE_SPECIAL": "false",
        
        # 安全配置
        "MAX_LOGIN_ATTEMPTS": "5",
        "LOCKOUT_DURATION": "300",
        "SESSION_TIMEOUT": "3600",
        "MAX_CONCURRENT_SESSIONS": "5",
        
        # 用户管理配置
        "ENABLE_USER_REGISTRATION": "true",
        "REQUIRE_EMAIL_VERIFICATION": "false",
        "DEFAULT_USER_ROLE": "user",
        
        # 邮件配置 (可选)
        "SMTP_HOST": "",
        "SMTP_PORT": "587",
        "SMTP_USERNAME": "",
        "SMTP_PASSWORD": "",
        "SMTP_USE_TLS": "true",
    }
    
    return create_env_template("User Service", "USER", additional_vars)


if __name__ == "__main__":
    # 测试配置加载
    try:
        settings = get_settings()
        print("✅ 用户服务配置加载成功")
        print(f"API端口: {settings.api_port}")
        print(f"数据库URL: {settings.database_url}")
        print(f"JWT过期时间: {settings.jwt_expire_minutes}分钟")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
    
    # 生成.env.example文件
    env_content = create_env_example()
    with open("user_service/.env.example", "w", encoding="utf-8") as f:
        f.write(env_content)
    print("✅ 已生成 user_service/.env.example 文件")
