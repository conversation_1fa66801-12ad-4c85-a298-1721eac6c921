# 知深学习导师 - 项目依赖包
# 基于Python 3.9+的完整依赖列表

# ===== 核心框架 =====
fastapi==0.104.1              # 现代Web框架
uvicorn[standard]==0.24.0     # ASGI服务器
pydantic==2.5.0               # 数据验证和序列化
pydantic-settings==2.1.0     # 配置管理

# ===== 数据库相关 =====
sqlalchemy==2.0.23           # ORM框架
alembic==1.13.1              # 数据库迁移
psycopg2-binary==2.9.9       # PostgreSQL驱动
pymysql==1.1.0               # MySQL驱动 (Manticore)
redis==5.0.1                 # Redis客户端

# ===== AI和机器学习 =====
openai==1.3.7                # OpenAI API客户端
anthropic==0.7.8             # Claude API客户端
sentence-transformers==2.2.2  # 本地embedding模型
transformers==4.36.2         # Hugging Face模型
torch==2.1.2                 # PyTorch (CPU版本)
numpy==1.24.3                # 数值计算
scikit-learn==1.3.2          # 机器学习工具

# ===== 文档处理 =====
python-multipart==0.0.6      # 文件上传支持
python-docx==1.1.0           # Word文档处理
PyPDF2==3.0.1                # PDF文档处理
markdown==3.5.1              # Markdown处理
beautifulsoup4==4.12.2       # HTML解析

# ===== 网络和通信 =====
httpx==0.25.2                # HTTP客户端
websockets==12.0             # WebSocket支持
aioredis==2.0.1              # 异步Redis客户端
celery==5.3.4                # 异步任务队列

# ===== 安全认证 =====
python-jose[cryptography]==3.3.0  # JWT处理
passlib[bcrypt]==1.7.4            # 密码加密
python-multipart==0.0.6           # 表单数据处理

# ===== 日志和监控 =====
structlog==23.2.0            # 结构化日志
prometheus-client==0.19.0    # Prometheus监控
psutil==5.9.6                # 系统监控

# ===== 开发工具 =====
pytest==7.4.3               # 测试框架
pytest-asyncio==0.21.1      # 异步测试
pytest-cov==4.1.0           # 测试覆盖率
black==23.11.0               # 代码格式化
isort==5.12.0                # 导入排序
flake8==6.1.0                # 代码检查
mypy==1.7.1                  # 类型检查

# ===== 配置和环境 =====
python-dotenv==1.0.0         # 环境变量管理
click==8.1.7                 # 命令行工具
rich==13.7.0                 # 终端美化输出
typer==0.9.0                 # 现代CLI框架

# ===== 缓存和存储 =====
diskcache==5.6.3             # 磁盘缓存
joblib==1.3.2                # 并行处理和缓存

# ===== 工具库 =====
python-dateutil==2.8.2       # 日期处理
pytz==2023.3                 # 时区处理
uuid==1.30                   # UUID生成
hashlib-compat==1.0.1        # 哈希兼容性

# ===== 特定模块依赖 =====

# embedding_service 特定依赖
sentence-transformers==2.2.2  # 本地embedding模型
faiss-cpu==1.7.4             # 向量相似度搜索 (可选)

# user_service 特定依赖
python-jose[cryptography]==3.3.0  # JWT处理
passlib[bcrypt]==1.7.4            # 密码加密

# document_service 特定依赖
python-magic==0.4.27         # 文件类型检测
chardet==5.2.0               # 字符编码检测

# conversation_service 特定依赖
websockets==12.0             # WebSocket支持
aioredis==2.0.1              # 异步Redis

# web_ui 相关 (如果使用Python后端渲染)
jinja2==3.1.2                # 模板引擎
starlette==0.27.0            # ASGI框架核心

# ===== 可选依赖 (根据需要安装) =====
# tensorflow==2.15.0         # TensorFlow (如果需要)
# torch-audio==2.1.2         # 音频处理 (如果需要)
# opencv-python==********    # 图像处理 (如果需要)
# spacy==3.7.2               # NLP处理 (如果需要)

# ===== 生产环境依赖 =====
gunicorn==21.2.0             # WSGI服务器
supervisor==4.2.5            # 进程管理
nginx==1.25.3                # 反向代理 (通过系统包管理器安装)

# ===== 版本固定说明 =====
# 所有版本都经过测试，确保兼容性
# 如需升级版本，请先在开发环境测试
# 生产环境建议使用 pip freeze > requirements.lock 锁定版本
