# 知深学习导师 - 完整服务编排
# 包含所有核心服务和基础设施组件

version: '3.8'

services:
  # ===== 数据存储层 =====
  
  # PostgreSQL - 主数据库
  postgres:
    image: postgres:15-alpine
    container_name: master-know-postgres
    environment:
      POSTGRES_DB: master_know
      POSTGRES_USER: master_know_user
      POSTGRES_PASSWORD: master_know_pass
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - master-know-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U master_know_user -d master_know"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis - 缓存和会话存储
  redis:
    image: redis:7-alpine
    container_name: master-know-redis
    command: redis-server --appendonly yes --requirepass master_know_redis_pass
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - master-know-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Manticore Search - 搜索引擎 (已完成模块)
  manticore:
    image: manticoresearch/manticore:latest
    container_name: master-know-manticore
    environment:
      - EXTRA=1
    volumes:
      - manticore_data:/var/lib/manticore
      - ./manticore_search/manticore.conf:/etc/manticoresearch/manticore.conf
    ports:
      - "9306:9306"  # MySQL协议
      - "9308:9308"  # HTTP协议
    networks:
      - master-know-network
    healthcheck:
      test: ["CMD-SHELL", "mysql -h127.0.0.1 -P9306 -e 'SHOW TABLES'"]
      interval: 10s
      timeout: 5s
      retries: 5

  # ===== 核心服务层 =====

  # 向量化引擎 (Priority 1)
  embedding-service:
    build:
      context: ./embedding_service
      dockerfile: Dockerfile
    container_name: master-know-embedding
    environment:
      - REDIS_URL=redis://:master_know_redis_pass@redis:6379
      - EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
      - API_HOST=0.0.0.0
      - API_PORT=8001
    ports:
      - "8001:8001"
    volumes:
      - embedding_models:/app/models  # 模型缓存
    networks:
      - master-know-network
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 用户服务 (Priority 2)
  user-service:
    build:
      context: ./user_service
      dockerfile: Dockerfile
    container_name: master-know-user
    environment:
      - DATABASE_URL=************************************************************/master_know
      - REDIS_URL=redis://:master_know_redis_pass@redis:6379
      - JWT_SECRET_KEY=your_jwt_secret_key_change_in_production
      - API_HOST=0.0.0.0
      - API_PORT=8002
    ports:
      - "8002:8002"
    networks:
      - master-know-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # API网关 (Priority 3)
  api-gateway:
    build:
      context: ./api_gateway
      dockerfile: Dockerfile
    container_name: master-know-gateway
    environment:
      - USER_SERVICE_URL=http://user-service:8002
      - EMBEDDING_SERVICE_URL=http://embedding-service:8001
      - TOPIC_SERVICE_URL=http://topic-service:8004
      - DOCUMENT_SERVICE_URL=http://document-service:8005
      - LLM_INTEGRATION_URL=http://llm-integration:8006
      - CONVERSATION_SERVICE_URL=http://conversation-service:8007
      - SUMMARY_SERVICE_URL=http://summary-service:8008
      - API_HOST=0.0.0.0
      - API_PORT=8003
    ports:
      - "8003:8003"
    networks:
      - master-know-network
    depends_on:
      - user-service
      - embedding-service
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8003/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 主题服务 (Priority 4)
  topic-service:
    build:
      context: ./topic_service
      dockerfile: Dockerfile
    container_name: master-know-topic
    environment:
      - DATABASE_URL=************************************************************/master_know
      - USER_SERVICE_URL=http://user-service:8002
      - API_HOST=0.0.0.0
      - API_PORT=8004
    ports:
      - "8004:8004"
    networks:
      - master-know-network
    depends_on:
      postgres:
        condition: service_healthy
      user-service:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8004/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 文档服务 (Priority 5)
  document-service:
    build:
      context: ./document_service
      dockerfile: Dockerfile
    container_name: master-know-document
    environment:
      - DATABASE_URL=************************************************************/master_know
      - MANTICORE_HOST=manticore
      - MANTICORE_PORT=9306
      - EMBEDDING_SERVICE_URL=http://embedding-service:8001
      - API_HOST=0.0.0.0
      - API_PORT=8005
    ports:
      - "8005:8005"
    volumes:
      - document_storage:/app/storage  # 文档存储
    networks:
      - master-know-network
    depends_on:
      postgres:
        condition: service_healthy
      manticore:
        condition: service_healthy
      embedding-service:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8005/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # LLM集成服务 (Priority 6)
  llm-integration:
    build:
      context: ./llm_integration
      dockerfile: Dockerfile
    container_name: master-know-llm
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - MANTICORE_HOST=manticore
      - MANTICORE_PORT=9306
      - EMBEDDING_SERVICE_URL=http://embedding-service:8001
      - API_HOST=0.0.0.0
      - API_PORT=8006
    ports:
      - "8006:8006"
    networks:
      - master-know-network
    depends_on:
      manticore:
        condition: service_healthy
      embedding-service:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8006/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 对话服务 (Priority 7)
  conversation-service:
    build:
      context: ./conversation_service
      dockerfile: Dockerfile
    container_name: master-know-conversation
    environment:
      - DATABASE_URL=************************************************************/master_know
      - REDIS_URL=redis://:master_know_redis_pass@redis:6379
      - LLM_INTEGRATION_URL=http://llm-integration:8006
      - MANTICORE_HOST=manticore
      - MANTICORE_PORT=9306
      - API_HOST=0.0.0.0
      - API_PORT=8007
    ports:
      - "8007:8007"
    networks:
      - master-know-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      llm-integration:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8007/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 摘要服务 (Priority 8)
  summary-service:
    build:
      context: ./summary_service
      dockerfile: Dockerfile
    container_name: master-know-summary
    environment:
      - DATABASE_URL=************************************************************/master_know
      - LLM_INTEGRATION_URL=http://llm-integration:8006
      - MANTICORE_HOST=manticore
      - MANTICORE_PORT=9306
      - API_HOST=0.0.0.0
      - API_PORT=8008
    ports:
      - "8008:8008"
    networks:
      - master-know-network
    depends_on:
      postgres:
        condition: service_healthy
      llm-integration:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8008/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ===== 前端服务 =====

  # Web UI (Priority 9)
  web-ui:
    build:
      context: ./web_ui
      dockerfile: Dockerfile
    container_name: master-know-web
    environment:
      - REACT_APP_API_GATEWAY_URL=http://localhost:8003
      - REACT_APP_WS_URL=ws://localhost:8007
    ports:
      - "3000:3000"
    networks:
      - master-know-network
    depends_on:
      - api-gateway
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ===== 监控和管理 =====

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: master-know-nginx
    volumes:
      - ./scripts/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./scripts/nginx/ssl:/etc/nginx/ssl
    ports:
      - "80:80"
      - "443:443"
    networks:
      - master-know-network
    depends_on:
      - api-gateway
      - web-ui
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

# ===== 网络配置 =====
networks:
  master-know-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# ===== 数据卷配置 =====
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  manticore_data:
    driver: local
  embedding_models:
    driver: local
  document_storage:
    driver: local
