"""
对话服务健康检查

基于统一健康检查框架的对话服务健康检查实现
"""

import sys
import os
from typing import Dict

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))
from scripts.health.health_framework import HealthChecker, ServiceHealth, HealthStatus, DatabaseHealthChecker
from conversation_service.utils.config import get_settings


class ConversationServiceHealthChecker(HealthChecker):
    """对话服务健康检查器"""
    
    def __init__(self):
        super().__init__("conversation_service", "1.0.0")
        self.settings = get_settings()
    
    async def check_dependencies(self) -> Dict[str, ServiceHealth]:
        """检查依赖服务健康状态"""
        dependencies = {}
        # 检查数据库连接
        if hasattr(self.settings, 'database_url') and self.settings.database_url:
            db_health = await DatabaseHealthChecker.check_postgresql(
                self.settings.database_url,
                timeout=self.settings.health_check_timeout
            )
            dependencies["database"] = db_health
        # 检查Redis连接
        if (hasattr(self.settings, 'enable_redis') and self.settings.enable_redis and 
            hasattr(self.settings, 'redis_url') and self.settings.redis_url):
            redis_health = await DatabaseHealthChecker.check_redis(
                self.settings.redis_url,
                timeout=self.settings.health_check_timeout
            )
            dependencies["redis"] = redis_health
        # 检查user_service服务
        if hasattr(self.settings, 'user_service_url') and self.settings.user_service_url:
            user_service_health = await self.check_service_url(
                "user_service",
                self.settings.user_service_url,
                timeout=self.settings.health_check_timeout
            )
            dependencies["user_service"] = user_service_health
        # 检查topic_service服务
        if hasattr(self.settings, 'topic_service_url') and self.settings.topic_service_url:
            topic_service_health = await self.check_service_url(
                "topic_service",
                self.settings.topic_service_url,
                timeout=self.settings.health_check_timeout
            )
            dependencies["topic_service"] = topic_service_health
        # 检查llm_integration服务
        if hasattr(self.settings, 'llm_integration_url') and self.settings.llm_integration_url:
            llm_integration_health = await self.check_service_url(
                "llm_integration",
                self.settings.llm_integration_url,
                timeout=self.settings.health_check_timeout
            )
            dependencies["llm_integration"] = llm_integration_health
        # 检查summary_service服务
        if hasattr(self.settings, 'summary_service_url') and self.settings.summary_service_url:
            summary_service_health = await self.check_service_url(
                "summary_service",
                self.settings.summary_service_url,
                timeout=self.settings.health_check_timeout
            )
            dependencies["summary_service"] = summary_service_health
        
        return dependencies
    
    async def check_self(self) -> ServiceHealth:
        """检查自身服务健康状态"""
        try:
            # 检查服务基本功能
            uptime = self.get_uptime()
            
            details = {
                "websocket_enabled": getattr(self.settings, 'enable_websocket', True),
                "max_connections": getattr(self.settings, 'max_websocket_connections', 1000),
                "message_queue_enabled": getattr(self.settings, 'enable_message_queue', True),
            }
            
            # 确定状态
            status = HealthStatus.HEALTHY
            message = "对话服务 is operational"
            
            return ServiceHealth(
                name=self.service_name,
                status=status,
                response_time_ms=0.0,
                message=message,
                details=details,
                timestamp=self.get_current_timestamp()
            )
            
        except Exception as e:
            return ServiceHealth(
                name=self.service_name,
                status=HealthStatus.ERROR,
                response_time_ms=0.0,
                message=f"Self check failed: {str(e)}",
                timestamp=self.get_current_timestamp()
            )
    
    def get_uptime(self) -> float:
        """获取服务运行时间"""
        import time
        return time.time() - self.start_time
    
    def get_current_timestamp(self) -> float:
        """获取当前时间戳"""
        import time
        return time.time()


# 全局健康检查器实例
_health_checker = None

def get_health_checker() -> ConversationServiceHealthChecker:
    """获取健康检查器实例（单例模式）"""
    global _health_checker
    if _health_checker is None:
        _health_checker = ConversationServiceHealthChecker()
    return _health_checker


async def get_health_status():
    """获取健康状态（FastAPI兼容）"""
    checker = get_health_checker()
    return await checker.get_health_status()


async def get_ready_status():
    """获取就绪状态（Kubernetes就绪探针）"""
    checker = get_health_checker()
    health = await checker.get_health_status()
    
    # 就绪检查更严格，关键依赖都必须健康
    if health.status in [HealthStatus.HEALTHY, HealthStatus.WARNING]:
        return {
            "status": "ready",
            "service": "conversation_service",
            "timestamp": health.timestamp
        }
    else:
        return {
            "status": "not_ready",
            "service": "conversation_service",
            "message": health.message,
            "timestamp": health.timestamp
        }


if __name__ == "__main__":
    import asyncio
    
    async def test_health_check():
        """测试健康检查功能"""
        print("🧪 测试对话服务健康检查...")
        
        checker = get_health_checker()
        health = await checker.get_health_status()
        
        print(f"整体状态: {health.status}")
        print(f"服务名称: {health.service_name}")
        print(f"版本: {health.version}")
        print(f"运行时间: {health.uptime_seconds:.2f}秒")
        print(f"消息: {health.message}")
        
        print("\n服务检查结果:")
        for name, service in health.services.items():
            print(f"  {name}: {service.status} ({service.response_time_ms:.2f}ms)")
            if service.message:
                print(f"    消息: {service.message}")
        
        print(f"\n系统状态:")
        print(f"  CPU: {health.system.cpu_percent:.1f}%")
        print(f"  内存: {health.system.memory_percent:.1f}%")
        print(f"  磁盘: {health.system.disk_percent:.1f}%")
        print(f"  系统状态: {health.system.status}")
        
        # 测试就绪状态
        ready = await get_ready_status()
        print(f"\n就绪状态: {ready['status']}")
    
    asyncio.run(test_health_check())