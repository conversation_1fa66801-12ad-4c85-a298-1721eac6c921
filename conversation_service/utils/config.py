"""
对话服务配置管理

基于全局配置模板的对话服务专用配置
"""

from pydantic import Field, validator
from typing import Optional, List
from functools import lru_cache
import sys
import os

# 添加项目根目录到路径，以便导入全局配置模板
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))
from scripts.config.config_template import BaseServiceSettings, DatabaseMixin, RedisMixin, ExternalServiceMixin


class ConversationServiceSettings(BaseServiceSettings, DatabaseMixin, RedisMixin, ExternalServiceMixin):
    """对话服务配置类"""
    
    # ===== 服务特定配置 =====
    api_port: int = Field(
        default=9007,
        description="对话服务端口"
    )
    api_title: str = Field(
        default="Conversation Service API",
        description="对话服务API标题"
    )
    api_description: str = Field(
        default="实时对话管理和WebSocket服务",
        description="对话服务API描述"
    )
    
    # ===== 外部服务依赖 =====
    user_service_url: str = Field(
        default="http://localhost:9002",
        description="用户服务URL"
    )
    topic_service_url: str = Field(
        default="http://localhost:9004",
        description="主题服务URL"
    )
    llm_integration_url: str = Field(
        default="http://localhost:9006",
        description="LLM集成服务URL"
    )
    summary_service_url: str = Field(
        default="http://localhost:9008",
        description="摘要服务URL"
    )
    
    # ===== WebSocket配置 =====
    enable_websocket: bool = Field(
        default=True,
        description="是否启用WebSocket"
    )
    websocket_heartbeat_interval: int = Field(
        default=30,
        ge=10, le=300,
        description="WebSocket心跳间隔(秒)"
    )
    websocket_timeout: int = Field(
        default=300,  # 5分钟
        ge=60, le=3600,
        description="WebSocket连接超时时间(秒)"
    )
    max_websocket_connections: int = Field(
        default=1000,
        ge=10, le=10000,
        description="最大WebSocket连接数"
    )
    
    # ===== 对话管理配置 =====
    max_conversation_history: int = Field(
        default=50,
        ge=10, le=200,
        description="最大对话历史记录数"
    )
    conversation_timeout: int = Field(
        default=1800,  # 30分钟
        ge=300, le=7200,
        description="对话超时时间(秒)"
    )
    enable_conversation_persistence: bool = Field(
        default=True,
        description="是否启用对话持久化"
    )
    
    # ===== 消息处理配置 =====
    max_message_length: int = Field(
        default=2000,
        ge=100, le=10000,
        description="最大消息长度"
    )
    enable_message_queue: bool = Field(
        default=True,
        description="是否启用消息队列"
    )
    message_processing_timeout: int = Field(
        default=30,
        ge=5, le=120,
        description="消息处理超时时间(秒)"
    )
    
    # ===== 上下文管理配置 =====
    enable_context_building: bool = Field(
        default=True,
        description="是否启用上下文构建"
    )
    context_window_size: int = Field(
        default=10,
        ge=3, le=50,
        description="上下文窗口大小"
    )
    context_cache_ttl: int = Field(
        default=1800,  # 30分钟
        ge=300, le=7200,
        description="上下文缓存过期时间(秒)"
    )
    
    # ===== 实时功能配置 =====
    enable_typing_indicator: bool = Field(
        default=True,
        description="是否启用输入指示器"
    )
    enable_read_receipts: bool = Field(
        default=True,
        description="是否启用已读回执"
    )
    enable_presence_status: bool = Field(
        default=True,
        description="是否启用在线状态"
    )
    
    # ===== 安全配置 =====
    enable_rate_limiting: bool = Field(
        default=True,
        description="是否启用限流"
    )
    messages_per_minute: int = Field(
        default=30,
        ge=5, le=200,
        description="每分钟消息限制"
    )
    enable_spam_detection: bool = Field(
        default=True,
        description="是否启用垃圾消息检测"
    )
    
    @validator('user_service_url', 'topic_service_url', 'llm_integration_url', 'summary_service_url')
    def validate_service_urls(cls, v):
        if not v.startswith(('http://', 'https://')):
            raise ValueError("服务URL必须以http://或https://开头")
        return v.rstrip('/')
    
    class Config:
        env_prefix = "CONVERSATION_"
        env_file = ".env"
        case_sensitive = False


@lru_cache()
def get_settings() -> ConversationServiceSettings:
    """获取对话服务配置实例（单例模式）"""
    from scripts.config.config_template import get_service_settings
    return get_service_settings(ConversationServiceSettings)


def create_env_example() -> str:
    """创建对话服务的.env.example文件内容"""
    from scripts.config.config_template import create_env_template
    
    additional_vars = {
        # 数据库配置
        "DATABASE_URL": "postgresql://master_know_user:master_know_pass@localhost:5432/master_know",
        "DATABASE_POOL_SIZE": "10",
        "DATABASE_POOL_TIMEOUT": "30",
        "DATABASE_ECHO": "false",
        
        # Redis配置
        "REDIS_URL": "redis://localhost:6379",
        "REDIS_POOL_SIZE": "10",
        "REDIS_TIMEOUT": "5",
        "ENABLE_REDIS": "true",
        
        # 外部服务依赖
        "USER_SERVICE_URL": "http://localhost:9002",
        "TOPIC_SERVICE_URL": "http://localhost:9004",
        "LLM_INTEGRATION_URL": "http://localhost:9006",
        "SUMMARY_SERVICE_URL": "http://localhost:9008",
        
        # WebSocket配置
        "ENABLE_WEBSOCKET": "true",
        "WEBSOCKET_HEARTBEAT_INTERVAL": "30",
        "WEBSOCKET_TIMEOUT": "300",
        "MAX_WEBSOCKET_CONNECTIONS": "1000",
        
        # 对话管理配置
        "MAX_CONVERSATION_HISTORY": "50",
        "CONVERSATION_TIMEOUT": "1800",
        "ENABLE_CONVERSATION_PERSISTENCE": "true",
        
        # 消息处理配置
        "MAX_MESSAGE_LENGTH": "2000",
        "ENABLE_MESSAGE_QUEUE": "true",
        "MESSAGE_PROCESSING_TIMEOUT": "30",
        
        # 上下文管理配置
        "ENABLE_CONTEXT_BUILDING": "true",
        "CONTEXT_WINDOW_SIZE": "10",
        "CONTEXT_CACHE_TTL": "1800",
        
        # 实时功能配置
        "ENABLE_TYPING_INDICATOR": "true",
        "ENABLE_READ_RECEIPTS": "true",
        "ENABLE_PRESENCE_STATUS": "true",
        
        # 安全配置
        "ENABLE_RATE_LIMITING": "true",
        "MESSAGES_PER_MINUTE": "30",
        "ENABLE_SPAM_DETECTION": "true",
    }
    
    return create_env_template("Conversation Service", "CONVERSATION", additional_vars)


if __name__ == "__main__":
    # 测试配置加载
    try:
        settings = get_settings()
        print("✅ 对话服务配置加载成功")
        print(f"API端口: {settings.api_port}")
        print(f"WebSocket: {'启用' if settings.enable_websocket else '禁用'}")
        print(f"最大连接数: {settings.max_websocket_connections}")
        print(f"对话历史: {settings.max_conversation_history}")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
    
    # 生成.env.example文件
    env_content = create_env_example()
    with open("conversation_service/.env.example", "w", encoding="utf-8") as f:
        f.write(env_content)
    print("✅ 已生成 conversation_service/.env.example 文件")
