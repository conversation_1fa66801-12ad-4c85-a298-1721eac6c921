# 对话服务 (Conversation Service)

**优先级**: Priority 7 - 高级功能模块  
**状态**: 💬 待开发  
**依赖**: llm_integration, topic_service, manticore_search

## 🎯 模块职责

对话服务是知深学习导师的核心交互模块，负责管理用户与AI的对话交互，包括消息管理、对话历史、实时通信和上下文构建。

### 核心功能
- **消息管理**: 对话消息的存储和管理
- **对话历史**: 完整的对话历史记录
- **实时通信**: WebSocket实时消息传输
- **上下文构建**: 智能上下文信息构建
- **逻辑回合**: 对话的逻辑回合管理

## 🏗️ 技术架构

### 技术栈
- **Python 3.9+**: 主要开发语言
- **FastAPI**: API服务框架
- **WebSocket**: 实时通信
- **PostgreSQL**: 对话数据存储
- **Redis**: 实时会话缓存
- **Manticore Search**: 上下文检索

### 架构设计
```
┌─────────────────────────────────────────────────────────────┐
│                    FastAPI Application                      │
├─────────────────────────────────────────────────────────────┤
│                      API Layer                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ Conversation│ │  WebSocket  │ │   History   │          │
│  │   Routes    │ │  Handler    │ │   Routes    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                    Service Layer                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ Conversation│ │   Message   │ │  Context    │          │
│  │  Service    │ │  Service    │ │  Builder    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                  Integration Layer                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │     LLM     │ │ Manticore   │ │   Topic     │          │
│  │Integration  │ │   Search    │ │  Service    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                    Storage Layer                           │
│  ┌─────────────┐ ┌─────────────────────────────────────────┐ │
│  │ PostgreSQL  │ │              Redis                     │ │
│  │ (对话数据)   │ │           (实时会话)                    │ │
│  └─────────────┘ └─────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 📁 目录结构

```
conversation_service/
├── __init__.py              # 模块入口和导出
├── api/                     # API 层
│   ├── __init__.py
│   ├── main.py             # FastAPI 应用和路由
│   └── websocket.py        # WebSocket处理器
├── services/               # 业务逻辑层
│   ├── __init__.py
│   ├── conversation_service.py # 对话管理服务
│   ├── message_service.py      # 消息管理服务
│   ├── context_builder.py      # 上下文构建服务
│   └── websocket_manager.py    # WebSocket管理器
├── models/                 # 数据模型层
│   ├── __init__.py
│   ├── conversation.py     # 对话模型
│   ├── message.py         # 消息模型
│   └── context.py         # 上下文模型
├── utils/                  # 工具模块
│   ├── __init__.py
│   ├── config.py          # 配置管理
│   ├── database.py        # 数据库连接
│   └── exceptions.py      # 异常定义
├── tests/                  # 测试模块
│   ├── __init__.py
│   ├── test_conversation_service.py
│   ├── test_message_service.py
│   ├── test_context_builder.py
│   └── test_websocket.py
├── docs/                   # 文档目录
│   └── api-documentation.md
├── migrations/             # 数据库迁移
│   └── init_conversations.sql
├── requirements.txt        # 依赖包列表
├── test_module.py         # 模块测试脚本
└── README.md              # 模块说明 (本文件)
```

## 🚀 快速开始

### 环境要求
- Python 3.9+
- PostgreSQL 13+
- Redis 6+
- llm_integration (AI对话引擎)
- manticore_search (上下文检索)

### 1. 安装依赖
```bash
cd conversation_service
source ../.venv/bin/activate
pip install -r requirements.txt
```

### 2. 初始化数据库
```bash
python -c "from utils.database import init_db; init_db()"
```

### 3. 启动服务
```bash
uvicorn api.main:app --reload --port 8007
```

## 💻 使用示例

### 创建对话
```python
from conversation_service import ConversationService, ConversationCreate

conversation_service = ConversationService()

# 创建新对话
conversation_data = ConversationCreate(
    topic_id=1,
    user_id=1,
    title="Python学习讨论"
)

conversation = conversation_service.create_conversation(conversation_data)
print(f"对话创建成功: {conversation.id}")
```

### 发送消息
```python
from conversation_service import MessageService, MessageCreate

message_service = MessageService()

# 用户发送消息
user_message = MessageCreate(
    conversation_id=conversation.id,
    role="user",
    content="请解释Python的装饰器概念"
)

message = message_service.create_message(user_message)
```

### WebSocket实时通信
```python
# 前端JavaScript示例
const ws = new WebSocket('ws://localhost:8007/ws/conversation/1');

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    if (data.type === 'ai_response') {
        displayMessage(data.content);
    }
};

// 发送消息
ws.send(JSON.stringify({
    type: 'user_message',
    content: '请解释Python的装饰器概念'
}));
```

## 🔗 API接口设计

### 对话管理接口
```python
# 创建对话
POST /api/v1/conversations
{
    "topic_id": 1,
    "title": "Python学习讨论"
}

# 获取对话列表
GET /api/v1/conversations?topic_id=1&limit=20

# 获取对话详情
GET /api/v1/conversations/{conversation_id}

# 获取对话消息
GET /api/v1/conversations/{conversation_id}/messages?limit=50
```

### WebSocket接口
```python
# WebSocket连接
WS /ws/conversation/{conversation_id}

# 消息格式
{
    "type": "user_message",
    "content": "用户消息内容",
    "timestamp": "2025-08-13T10:00:00Z"
}

{
    "type": "ai_response", 
    "content": "AI回复内容",
    "turn_id": 123,
    "timestamp": "2025-08-13T10:00:01Z"
}
```

## 🗄️ 数据模型设计

### 对话表 (conversations)
```sql
CREATE TABLE conversations (
    id BIGSERIAL PRIMARY KEY,
    topic_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    title TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

CREATE INDEX idx_conversations_topic ON conversations(topic_id);
CREATE INDEX idx_conversations_user ON conversations(user_id);
```

### 消息表 (messages)
```sql
CREATE TABLE messages (
    id BIGSERIAL PRIMARY KEY,
    conversation_id BIGINT NOT NULL,
    turn_id BIGINT NOT NULL,
    role VARCHAR(20) NOT NULL, -- 'user' or 'assistant'
    content TEXT NOT NULL,
    summary JSONB, -- 结构化摘要
    summary_vector VECTOR(384), -- 摘要向量
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_messages_conversation ON messages(conversation_id);
CREATE INDEX idx_messages_turn ON messages(turn_id);
CREATE INDEX idx_messages_summary_vector ON messages USING hnsw(summary_vector);
```

## 🧠 智能上下文构建

### 上下文构建策略
```python
class ContextBuilder:
    def build_context(self, conversation_id: int, current_message: str) -> str:
        """构建智能上下文"""
        
        # 1. 获取短期记忆 (最近消息)
        recent_messages = self.get_recent_messages(conversation_id, limit=10)
        
        # 2. 获取长期记忆 (相关摘要)
        relevant_summaries = self.search_relevant_summaries(
            current_message, 
            conversation_id
        )
        
        # 3. 获取知识库 (相关文档)
        relevant_docs = self.search_relevant_documents(
            current_message,
            topic_id=conversation.topic_id
        )
        
        # 4. 构建完整上下文
        context = self.build_prompt(
            recent_messages=recent_messages,
            relevant_summaries=relevant_summaries,
            relevant_docs=relevant_docs,
            current_message=current_message
        )
        
        return context
```

### 与Manticore Search集成
```python
def search_relevant_summaries(self, query: str, conversation_id: int) -> List[Dict]:
    """搜索相关的对话摘要"""
    from manticore_search import SearchService, create_vector_search_request
    
    # 向量化查询
    query_vector = embedding_service.embed_text(query)
    
    # 搜索相关摘要
    search_request = create_vector_search_request(
        query=query,
        query_vector=query_vector.embedding,
        limit=5,
        filters={"conversation_id": conversation_id}
    )
    
    return search_service.search(search_request)
```

## 🔄 逻辑回合管理

### 回合定义
- **逻辑回合**: 从用户提问到AI完成回答的完整交互
- **回合ID**: 用于关联同一回合的多条消息
- **摘要触发**: 每个回合结束后触发摘要生成

### 回合管理流程
```python
class TurnManager:
    def start_new_turn(self, conversation_id: int) -> int:
        """开始新的逻辑回合"""
        turn_id = self.generate_turn_id()
        self.cache_turn_state(turn_id, "started")
        return turn_id
    
    def complete_turn(self, turn_id: int):
        """完成逻辑回合"""
        self.cache_turn_state(turn_id, "completed")
        # 触发摘要生成
        self.trigger_summary_generation(turn_id)
```

## 🔗 与其他模块集成

### 与LLM Integration集成
```python
async def process_user_message(self, message: MessageCreate) -> str:
    """处理用户消息并获取AI回复"""
    
    # 构建上下文
    context = context_builder.build_context(
        message.conversation_id, 
        message.content
    )
    
    # 调用LLM服务
    from llm_integration import LLMService
    llm_response = await llm_service.generate_response(
        context=context,
        user_message=message.content,
        stream=True
    )
    
    return llm_response
```

### 与Summary Service集成
```python
def trigger_summary_generation(self, turn_id: int):
    """触发摘要生成"""
    from summary_service import SummaryService
    
    # 异步触发摘要生成
    summary_service.generate_turn_summary.delay(turn_id)
```

## 🧪 测试策略

### 单元测试
- 对话CRUD操作测试
- 消息管理测试
- 上下文构建测试
- WebSocket连接测试

### 集成测试
- LLM集成测试
- Manticore Search集成测试
- 实时通信测试

### 性能测试
- WebSocket并发测试
- 上下文构建性能测试
- 消息存储性能测试

## 📊 性能指标

### 目标性能
- **消息响应**: <100ms
- **上下文构建**: <200ms
- **WebSocket延迟**: <50ms
- **并发连接**: 1000+

## 🔧 配置说明

### 环境变量
```bash
# 数据库配置
DATABASE_URL=postgresql://user:pass@localhost:5432/master_know
REDIS_URL=redis://localhost:6379

# 服务依赖
LLM_INTEGRATION_URL=http://localhost:8006
MANTICORE_SEARCH_URL=http://localhost:8000
SUMMARY_SERVICE_URL=http://localhost:8008

# WebSocket配置
WS_MAX_CONNECTIONS=1000
WS_HEARTBEAT_INTERVAL=30

# 上下文配置
MAX_RECENT_MESSAGES=10
MAX_RELEVANT_SUMMARIES=5
MAX_RELEVANT_DOCS=3

# API配置
API_HOST=0.0.0.0
API_PORT=8007
```

---

**模块版本**: 1.0.0  
**创建日期**: 2025-08-13  
**维护者**: Winston (Architect)
