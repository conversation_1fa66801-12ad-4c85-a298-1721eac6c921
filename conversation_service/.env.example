# Conversation Service Configuration

# ===== API服务配置 =====
CONVERSATION_API_HOST=0.0.0.0
CONVERSATION_API_PORT=9007
CONVERSATION_API_TITLE="Conversation Service API"
CONVERSATION_API_DESCRIPTION="实时对话管理和WebSocket服务"
CONVERSATION_API_VERSION=1.0.0
CONVERSATION_API_PREFIX=/api/v1

# ===== 日志配置 =====
CONVERSATION_LOG_LEVEL=INFO
CONVERSATION_LOG_FORMAT=json
CONVERSATION_ENABLE_REQUEST_LOGGING=true

# ===== 健康检查配置 =====
CONVERSATION_HEALTH_CHECK_TIMEOUT=5
CONVERSATION_ENABLE_HEALTH_CHECK=true

# ===== 安全配置 =====
CONVERSATION_ENABLE_CORS=true
CONVERSATION_CORS_ORIGINS=["*"]

# ===== 环境配置 =====
CONVERSATION_ENVIRONMENT=development
CONVERSATION_DEBUG=false

# ===== 外部服务配置 =====
CONVERSATION_REQUEST_TIMEOUT=30
CONVERSATION_MAX_RETRIES=3
CONVERSATION_RETRY_DELAY=1.0

# ===== 服务特定配置 =====
# 数据库配置
CONVERSATION_DATABASE_URL=postgresql://master_know_user:master_know_pass@localhost:5432/master_know
CONVERSATION_DATABASE_POOL_SIZE=10
CONVERSATION_DATABASE_POOL_TIMEOUT=30
CONVERSATION_DATABASE_ECHO=false

# Redis配置
CONVERSATION_REDIS_URL=redis://localhost:6379
CONVERSATION_REDIS_POOL_SIZE=10
CONVERSATION_REDIS_TIMEOUT=5
CONVERSATION_ENABLE_REDIS=true

# 外部服务依赖
CONVERSATION_USER_SERVICE_URL=http://localhost:9002
CONVERSATION_TOPIC_SERVICE_URL=http://localhost:9004
CONVERSATION_LLM_INTEGRATION_URL=http://localhost:9006
CONVERSATION_SUMMARY_SERVICE_URL=http://localhost:9008

# WebSocket配置
CONVERSATION_ENABLE_WEBSOCKET=true
CONVERSATION_WEBSOCKET_HEARTBEAT_INTERVAL=30
CONVERSATION_WEBSOCKET_TIMEOUT=300
CONVERSATION_MAX_WEBSOCKET_CONNECTIONS=1000

# 对话管理配置
CONVERSATION_MAX_CONVERSATION_HISTORY=50
CONVERSATION_CONVERSATION_TIMEOUT=1800
CONVERSATION_ENABLE_CONVERSATION_PERSISTENCE=true

# 消息处理配置
CONVERSATION_MAX_MESSAGE_LENGTH=2000
CONVERSATION_ENABLE_MESSAGE_QUEUE=true
CONVERSATION_MESSAGE_PROCESSING_TIMEOUT=30

# 上下文管理配置
CONVERSATION_ENABLE_CONTEXT_BUILDING=true
CONVERSATION_CONTEXT_WINDOW_SIZE=10
CONVERSATION_CONTEXT_CACHE_TTL=1800

# 实时功能配置
CONVERSATION_ENABLE_TYPING_INDICATOR=true
CONVERSATION_ENABLE_READ_RECEIPTS=true
CONVERSATION_ENABLE_PRESENCE_STATUS=true

# 安全配置
CONVERSATION_ENABLE_RATE_LIMITING=true
CONVERSATION_MESSAGES_PER_MINUTE=30
CONVERSATION_ENABLE_SPAM_DETECTION=true
