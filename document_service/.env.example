# Document Service Configuration

# ===== API服务配置 =====
DOCUMENT_API_HOST=0.0.0.0
DOCUMENT_API_PORT=9005
DOCUMENT_API_TITLE="Document Service API"
DOCUMENT_API_DESCRIPTION="文档上传、解析和管理服务"
DOCUMENT_API_VERSION=1.0.0
DOCUMENT_API_PREFIX=/api/v1

# ===== 日志配置 =====
DOCUMENT_LOG_LEVEL=INFO
DOCUMENT_LOG_FORMAT=json
DOCUMENT_ENABLE_REQUEST_LOGGING=true

# ===== 健康检查配置 =====
DOCUMENT_HEALTH_CHECK_TIMEOUT=5
DOCUMENT_ENABLE_HEALTH_CHECK=true

# ===== 安全配置 =====
DOCUMENT_ENABLE_CORS=true
DOCUMENT_CORS_ORIGINS=["*"]

# ===== 环境配置 =====
DOCUMENT_ENVIRONMENT=development
DOCUMENT_DEBUG=false

# ===== 外部服务配置 =====
DOCUMENT_REQUEST_TIMEOUT=30
DOCUMENT_MAX_RETRIES=3
DOCUMENT_RETRY_DELAY=1.0

# ===== 服务特定配置 =====
# 数据库配置
DOCUMENT_DATABASE_URL=postgresql://master_know_user:master_know_pass@localhost:5432/master_know
DOCUMENT_DATABASE_POOL_SIZE=10
DOCUMENT_DATABASE_POOL_TIMEOUT=30
DOCUMENT_DATABASE_ECHO=false

# 外部服务依赖
DOCUMENT_EMBEDDING_SERVICE_URL=http://localhost:9001
DOCUMENT_MANTICORE_SEARCH_URL=http://localhost:9000

# 文件存储配置
DOCUMENT_UPLOAD_DIR=./storage/uploads
DOCUMENT_PROCESSED_DIR=./storage/processed
DOCUMENT_MAX_FILE_SIZE=10485760
DOCUMENT_ALLOWED_EXTENSIONS=[".txt", ".md", ".pdf", ".docx"]

# 文档分块配置
DOCUMENT_MAX_CHUNK_SIZE=1000
DOCUMENT_OVERLAP_SIZE=100
DOCUMENT_MIN_CHUNK_SIZE=200

# 处理配置
DOCUMENT_ENABLE_ASYNC_PROCESSING=true
DOCUMENT_PROCESSING_TIMEOUT=300
DOCUMENT_MAX_CONCURRENT_JOBS=5

# 缓存配置
DOCUMENT_ENABLE_DOCUMENT_CACHE=true
DOCUMENT_DOCUMENT_CACHE_TTL=7200
