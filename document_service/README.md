# 文档服务 (Document Service)

**优先级**: Priority 5 - 核心功能模块 (立即开发)  
**状态**: 📄 待开发  
**依赖**: embedding_service, manticore_search

## 🎯 模块职责

文档服务是知深学习导师的核心功能模块，负责文档的上传、解析、分块和存储，为学习系统提供知识库基础。

### 核心功能
- **文档上传**: 支持.txt和.md格式文档上传
- **文档解析**: 智能解析文档内容和结构
- **智能分块**: 将长文档分割成合适的学习片段
- **向量化存储**: 与embedding_service集成，生成文档向量
- **搜索集成**: 与manticore_search集成，支持全文和语义搜索

## 🏗️ 技术架构

### 技术栈
- **Python 3.9+**: 主要开发语言
- **FastAPI**: API服务框架
- **PostgreSQL**: 文档元数据存储
- **Manticore Search**: 文档内容和向量存储
- **python-multipart**: 文件上传支持
- **markdown**: Markdown文档解析

### 架构设计
```
┌─────────────────────────────────────────────────────────────┐
│                    FastAPI Application                      │
├─────────────────────────────────────────────────────────────┤
│                      API Layer                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │  Document   │ │   Upload    │ │   Search    │          │
│  │   Routes    │ │   Routes    │ │   Routes    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                    Service Layer                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │  Document   │ │   Parser    │ │   Chunker   │          │
│  │  Service    │ │  Service    │ │  Service    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│                Integration Layer                           │
│  ┌─────────────┐ ┌─────────────────────────────────────────┐ │
│  │ Embedding   │ │         Manticore Search               │ │
│  │  Service    │ │       (Content Storage)                │ │
│  └─────────────┘ └─────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    Storage Layer                           │
│  ┌─────────────┐ ┌─────────────────────────────────────────┐ │
│  │ PostgreSQL  │ │            File System                 │ │
│  │ (元数据)     │ │          (原始文件)                     │ │
│  └─────────────┘ └─────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 📁 目录结构

```
document_service/
├── __init__.py              # 模块入口和导出
├── api/                     # API 层
│   ├── __init__.py
│   └── main.py             # FastAPI 应用和路由
├── services/               # 业务逻辑层
│   ├── __init__.py
│   ├── document_service.py # 文档管理服务
│   ├── parser_service.py   # 文档解析服务
│   ├── chunker_service.py  # 文档分块服务
│   └── storage_service.py  # 存储管理服务
├── models/                 # 数据模型层
│   ├── __init__.py
│   ├── document.py        # 文档模型
│   ├── chunk.py           # 文档块模型
│   └── upload.py          # 上传模型
├── utils/                  # 工具模块
│   ├── __init__.py
│   ├── config.py          # 配置管理
│   ├── file_utils.py      # 文件处理工具
│   ├── database.py        # 数据库连接
│   └── exceptions.py      # 异常定义
├── tests/                  # 测试模块
│   ├── __init__.py
│   ├── test_document_service.py
│   ├── test_parser_service.py
│   ├── test_chunker_service.py
│   └── test_upload.py
├── docs/                   # 文档目录
│   └── api-documentation.md
├── storage/               # 文件存储目录
│   ├── uploads/           # 上传文件
│   └── processed/         # 处理后文件
├── migrations/             # 数据库迁移
│   └── init_documents.sql
├── requirements.txt        # 依赖包列表
├── test_module.py         # 模块测试脚本
└── README.md              # 模块说明 (本文件)
```

## 🚀 快速开始

### 环境要求
- Python 3.9+
- PostgreSQL 13+
- embedding_service (向量化)
- manticore_search (搜索存储)

### 1. 安装依赖
```bash
cd document_service
source ../.venv/bin/activate
pip install -r requirements.txt
```

### 2. 创建存储目录
```bash
mkdir -p storage/uploads storage/processed
```

### 3. 初始化数据库
```bash
python -c "from utils.database import init_db; init_db()"
```

### 4. 启动服务
```bash
uvicorn api.main:app --reload --port 8005
```

## 💻 使用示例

### 上传文档
```python
from document_service import DocumentService
import aiofiles

document_service = DocumentService()

# 上传文档
async def upload_document():
    with open("python_tutorial.md", "rb") as file:
        document = await document_service.upload_document(
            file=file,
            filename="python_tutorial.md",
            title="Python编程教程",
            user_id=1  # 固定测试用户
        )
    
    print(f"文档上传成功: {document.id}")
    return document
```

### 获取文档列表
```python
# 获取用户的所有文档
documents = document_service.get_user_documents(
    user_id=1,
    limit=20,
    offset=0
)

for doc in documents:
    print(f"文档: {doc.title} - 块数: {doc.chunk_count}")
```

### 搜索文档内容
```python
# 在文档中搜索内容
search_results = document_service.search_documents(
    query="Python装饰器",
    user_id=1,
    limit=10
)

for result in search_results:
    print(f"匹配: {result.title} - 相关度: {result.score}")
```

## 🔗 API接口设计

### 文档管理接口
```python
# 上传文档
POST /api/v1/documents
Content-Type: multipart/form-data
{
    "file": <file_data>,
    "title": "文档标题",
    "description": "文档描述"  # 可选
}

# 获取文档列表
GET /api/v1/documents?limit=20&offset=0

# 获取文档详情
GET /api/v1/documents/{document_id}

# 获取文档内容块
GET /api/v1/documents/{document_id}/chunks

# 删除文档
DELETE /api/v1/documents/{document_id}
```

### 搜索接口
```python
# 搜索文档
POST /api/v1/documents/search
{
    "query": "搜索关键词",
    "search_type": "hybrid",  # fulltext, vector, hybrid
    "limit": 10
}

# 获取相似文档
POST /api/v1/documents/{document_id}/similar
{
    "limit": 5
}
```

## 🗄️ 数据模型设计

### 文档表 (documents)
```sql
CREATE TABLE documents (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    filename TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    chunk_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_processed BOOLEAN DEFAULT FALSE
);

CREATE INDEX idx_documents_user_id ON documents(user_id);
CREATE INDEX idx_documents_title ON documents USING gin(to_tsvector('english', title));
```

### 文档块表 (document_chunks)
```sql
CREATE TABLE document_chunks (
    id BIGSERIAL PRIMARY KEY,
    document_id BIGINT NOT NULL,
    chunk_index INTEGER NOT NULL,
    content TEXT NOT NULL,
    chunk_type VARCHAR(50) DEFAULT 'paragraph',
    word_count INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(document_id, chunk_index)
);

CREATE INDEX idx_chunks_document ON document_chunks(document_id);
```

## 🔧 智能分块策略

### 分块算法
```python
class ChunkerService:
    def __init__(self):
        self.max_chunk_size = 1000  # 最大字符数
        self.overlap_size = 100     # 重叠字符数
        self.min_chunk_size = 200   # 最小字符数
    
    def chunk_document(self, content: str, doc_type: str) -> List[str]:
        """智能文档分块"""
        
        if doc_type == "markdown":
            return self.chunk_markdown(content)
        else:
            return self.chunk_text(content)
    
    def chunk_markdown(self, content: str) -> List[str]:
        """Markdown文档分块"""
        chunks = []
        
        # 按标题分割
        sections = self.split_by_headers(content)
        
        for section in sections:
            if len(section) <= self.max_chunk_size:
                chunks.append(section)
            else:
                # 大段落进一步分割
                sub_chunks = self.split_large_section(section)
                chunks.extend(sub_chunks)
        
        return chunks
    
    def chunk_text(self, content: str) -> List[str]:
        """纯文本分块"""
        chunks = []
        
        # 按段落分割
        paragraphs = content.split('\n\n')
        
        current_chunk = ""
        for paragraph in paragraphs:
            if len(current_chunk + paragraph) <= self.max_chunk_size:
                current_chunk += paragraph + "\n\n"
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = paragraph + "\n\n"
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return chunks
```

## 🔗 与其他模块集成

### 与Embedding Service集成
```python
async def process_document_chunks(self, document_id: int, chunks: List[str]):
    """处理文档块并生成向量"""
    from embedding_service import EmbeddingService
    
    embedding_service = EmbeddingService()
    
    # 批量向量化
    embeddings = await embedding_service.embed_batch(chunks)
    
    # 存储到Manticore
    for i, (chunk, embedding) in enumerate(zip(chunks, embeddings)):
        await self.store_chunk_with_vector(
            document_id=document_id,
            chunk_index=i,
            content=chunk,
            embedding=embedding
        )
```

### 与Manticore Search集成
```python
async def store_chunk_with_vector(self, document_id: int, chunk_index: int, 
                                 content: str, embedding: List[float]):
    """存储文档块到Manticore"""
    from manticore_search import DocumentService, DocumentCreate
    
    # 创建Manticore文档
    manticore_doc = DocumentCreate(
        title=f"doc_{document_id}_chunk_{chunk_index}",
        content=content,
        category="document_chunk",
        embedding=embedding
    )
    
    # 存储到Manticore
    result = manticore_service.create_document(manticore_doc)
    
    # 更新PostgreSQL中的关联信息
    await self.update_chunk_manticore_id(
        document_id, chunk_index, result.id
    )
```

## 🧪 测试策略

### 单元测试
- 文档上传测试
- 文档解析测试
- 分块算法测试
- 向量化集成测试

### 集成测试
- 端到端文档处理测试
- Manticore集成测试
- 搜索功能测试

### 性能测试
- 大文档处理性能
- 批量上传测试
- 搜索响应时间测试

## 📊 性能指标

### 目标性能
- **文档上传**: <5s (10MB文档)
- **文档解析**: <2s (普通文档)
- **分块处理**: <1s (1000段落)
- **搜索响应**: <200ms

## 🔧 配置说明

### 环境变量
```bash
# 数据库配置
DATABASE_URL=postgresql://user:pass@localhost:5432/master_know

# 服务依赖
EMBEDDING_SERVICE_URL=http://localhost:8001
MANTICORE_HOST=localhost
MANTICORE_PORT=9306

# 文件存储配置
UPLOAD_DIR=./storage/uploads
PROCESSED_DIR=./storage/processed
MAX_FILE_SIZE=10485760  # 10MB

# 分块配置
MAX_CHUNK_SIZE=1000
OVERLAP_SIZE=100
MIN_CHUNK_SIZE=200

# API配置
API_HOST=0.0.0.0
API_PORT=8005
```

---

**模块版本**: 1.0.0  
**创建日期**: 2025-08-13  
**维护者**: Winston (Architect)
