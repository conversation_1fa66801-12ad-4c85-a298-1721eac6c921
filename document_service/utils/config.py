"""
文档服务配置管理

基于全局配置模板的文档服务专用配置
"""

from pydantic import Field, validator
from typing import Optional, List
from functools import lru_cache
import sys
import os

# 添加项目根目录到路径，以便导入全局配置模板
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))
from scripts.config.config_template import BaseServiceSettings, DatabaseMixin, ExternalServiceMixin


class DocumentServiceSettings(BaseServiceSettings, DatabaseMixin, ExternalServiceMixin):
    """文档服务配置类"""
    
    # ===== 服务特定配置 =====
    api_port: int = Field(
        default=9005,
        description="文档服务端口"
    )
    api_title: str = Field(
        default="Document Service API",
        description="文档服务API标题"
    )
    api_description: str = Field(
        default="文档上传、解析和管理服务",
        description="文档服务API描述"
    )
    
    # ===== 外部服务依赖 =====
    embedding_service_url: str = Field(
        default="http://localhost:9001",
        description="向量化服务URL"
    )
    manticore_search_url: str = Field(
        default="http://localhost:9000",
        description="Manticore搜索服务URL"
    )
    
    # ===== 文件存储配置 =====
    upload_dir: str = Field(
        default="./storage/uploads",
        description="文件上传目录"
    )
    processed_dir: str = Field(
        default="./storage/processed",
        description="处理后文件目录"
    )
    max_file_size: int = Field(
        default=10485760,  # 10MB
        ge=1048576, le=104857600,  # 1MB到100MB
        description="最大文件大小(字节)"
    )
    allowed_extensions: List[str] = Field(
        default=[".txt", ".md", ".pdf", ".docx"],
        description="允许的文件扩展名"
    )
    
    # ===== 文档分块配置 =====
    max_chunk_size: int = Field(
        default=1000,
        ge=100, le=5000,
        description="最大分块大小(字符)"
    )
    overlap_size: int = Field(
        default=100,
        ge=0, le=500,
        description="分块重叠大小(字符)"
    )
    min_chunk_size: int = Field(
        default=200,
        ge=50, le=1000,
        description="最小分块大小(字符)"
    )
    
    # ===== 处理配置 =====
    enable_async_processing: bool = Field(
        default=True,
        description="是否启用异步处理"
    )
    processing_timeout: int = Field(
        default=300,  # 5分钟
        ge=60, le=1800,  # 1分钟到30分钟
        description="处理超时时间(秒)"
    )
    max_concurrent_jobs: int = Field(
        default=5,
        ge=1, le=20,
        description="最大并发处理任务数"
    )
    
    # ===== 缓存配置 =====
    enable_document_cache: bool = Field(
        default=True,
        description="是否启用文档缓存"
    )
    document_cache_ttl: int = Field(
        default=7200,  # 2小时
        ge=300, le=86400,  # 5分钟到24小时
        description="文档缓存过期时间(秒)"
    )
    
    @validator('embedding_service_url', 'manticore_search_url')
    def validate_service_urls(cls, v):
        if not v.startswith(('http://', 'https://')):
            raise ValueError("服务URL必须以http://或https://开头")
        return v.rstrip('/')
    
    @validator('upload_dir', 'processed_dir')
    def validate_directories(cls, v):
        # 确保目录存在
        os.makedirs(v, exist_ok=True)
        return v
    
    @validator('allowed_extensions')
    def validate_extensions(cls, v):
        # 确保扩展名以点开头
        return [ext if ext.startswith('.') else f'.{ext}' for ext in v]
    
    class Config:
        env_prefix = "DOCUMENT_"
        env_file = ".env"
        case_sensitive = False


@lru_cache()
def get_settings() -> DocumentServiceSettings:
    """获取文档服务配置实例（单例模式）"""
    from scripts.config.config_template import get_service_settings
    return get_service_settings(DocumentServiceSettings)


def create_env_example() -> str:
    """创建文档服务的.env.example文件内容"""
    from scripts.config.config_template import create_env_template
    
    additional_vars = {
        # 数据库配置
        "DATABASE_URL": "postgresql://master_know_user:master_know_pass@localhost:5432/master_know",
        "DATABASE_POOL_SIZE": "10",
        "DATABASE_POOL_TIMEOUT": "30",
        "DATABASE_ECHO": "false",
        
        # 外部服务依赖
        "EMBEDDING_SERVICE_URL": "http://localhost:9001",
        "MANTICORE_SEARCH_URL": "http://localhost:9000",
        
        # 文件存储配置
        "UPLOAD_DIR": "./storage/uploads",
        "PROCESSED_DIR": "./storage/processed",
        "MAX_FILE_SIZE": "10485760",
        "ALLOWED_EXTENSIONS": '[".txt", ".md", ".pdf", ".docx"]',
        
        # 文档分块配置
        "MAX_CHUNK_SIZE": "1000",
        "OVERLAP_SIZE": "100",
        "MIN_CHUNK_SIZE": "200",
        
        # 处理配置
        "ENABLE_ASYNC_PROCESSING": "true",
        "PROCESSING_TIMEOUT": "300",
        "MAX_CONCURRENT_JOBS": "5",
        
        # 缓存配置
        "ENABLE_DOCUMENT_CACHE": "true",
        "DOCUMENT_CACHE_TTL": "7200",
    }
    
    return create_env_template("Document Service", "DOCUMENT", additional_vars)


if __name__ == "__main__":
    # 测试配置加载
    try:
        settings = get_settings()
        print("✅ 文档服务配置加载成功")
        print(f"API端口: {settings.api_port}")
        print(f"上传目录: {settings.upload_dir}")
        print(f"最大文件大小: {settings.max_file_size} bytes")
        print(f"分块大小: {settings.max_chunk_size}")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
    
    # 生成.env.example文件
    env_content = create_env_example()
    with open("document_service/.env.example", "w", encoding="utf-8") as f:
        f.write(env_content)
    print("✅ 已生成 document_service/.env.example 文件")
