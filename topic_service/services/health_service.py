"""
主题服务健康检查

基于统一健康检查框架的主题服务健康检查实现
"""

import sys
import os
from typing import Dict

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))
from scripts.health.health_framework import HealthChecker, ServiceHealth, HealthStatus, DatabaseHealthChecker
from topic_service.utils.config import get_settings


class TopicServiceHealthChecker(HealthChecker):
    """主题服务健康检查器"""
    
    def __init__(self):
        super().__init__("topic_service", "1.0.0")
        self.settings = get_settings()
    
    async def check_dependencies(self) -> Dict[str, ServiceHealth]:
        """检查依赖服务健康状态"""
        dependencies = {}
        
        # 检查数据库连接
        if hasattr(self.settings, 'database_url') and self.settings.database_url:
            db_health = await DatabaseHealthChecker.check_postgresql(
                self.settings.database_url,
                timeout=self.settings.health_check_timeout
            )
            dependencies["database"] = db_health
        
        # 检查用户服务
        if hasattr(self.settings, 'user_service_url') and self.settings.user_service_url:
            user_service_health = await self.check_service_url(
                "user_service",
                self.settings.user_service_url,
                timeout=self.settings.health_check_timeout
            )
            dependencies["user_service"] = user_service_health
        
        # 检查文档服务
        if hasattr(self.settings, 'document_service_url') and self.settings.document_service_url:
            doc_service_health = await self.check_service_url(
                "document_service",
                self.settings.document_service_url,
                timeout=self.settings.health_check_timeout
            )
            dependencies["document_service"] = doc_service_health
        
        # 检查向量化服务
        if hasattr(self.settings, 'embedding_service_url') and self.settings.embedding_service_url:
            embedding_service_health = await self.check_service_url(
                "embedding_service",
                self.settings.embedding_service_url,
                timeout=self.settings.health_check_timeout
            )
            dependencies["embedding_service"] = embedding_service_health
        
        return dependencies
    
    async def check_self(self) -> ServiceHealth:
        """检查自身服务健康状态"""
        try:
            # 检查服务基本功能
            uptime = self.get_uptime()
            
            # 检查主题管理配置
            max_topics = getattr(self.settings, 'max_topics_per_user', 100)
            max_docs_per_topic = getattr(self.settings, 'max_documents_per_topic', 50)
            search_enabled = getattr(self.settings, 'enable_topic_search', True)
            cache_enabled = getattr(self.settings, 'enable_topic_cache', True)
            
            details = {
                "uptime_seconds": uptime,
                "max_topics_per_user": max_topics,
                "max_documents_per_topic": max_docs_per_topic,
                "search_enabled": search_enabled,
                "cache_enabled": cache_enabled,
                "statistics_enabled": getattr(self.settings, 'enable_statistics', True)
            }
            
            # 确定状态
            status = HealthStatus.HEALTHY
            message = "Topic service is operational"
            
            # 检查配置合理性
            if max_topics > 1000 or max_docs_per_topic > 500:
                status = HealthStatus.WARNING
                message = "Configuration limits are very high, may impact performance"
            
            return ServiceHealth(
                name=self.service_name,
                status=status,
                response_time_ms=0.0,
                message=message,
                details=details,
                timestamp=self.get_current_timestamp()
            )
            
        except Exception as e:
            return ServiceHealth(
                name=self.service_name,
                status=HealthStatus.ERROR,
                response_time_ms=0.0,
                message=f"Self check failed: {str(e)}",
                timestamp=self.get_current_timestamp()
            )
    
    def get_uptime(self) -> float:
        """获取服务运行时间"""
        import time
        return time.time() - self.start_time
    
    def get_current_timestamp(self) -> float:
        """获取当前时间戳"""
        import time
        return time.time()


# 全局健康检查器实例
_health_checker = None

def get_health_checker() -> TopicServiceHealthChecker:
    """获取健康检查器实例（单例模式）"""
    global _health_checker
    if _health_checker is None:
        _health_checker = TopicServiceHealthChecker()
    return _health_checker


async def get_health_status():
    """获取健康状态（FastAPI兼容）"""
    checker = get_health_checker()
    return await checker.get_health_status()


async def get_ready_status():
    """获取就绪状态（Kubernetes就绪探针）"""
    checker = get_health_checker()
    health = await checker.get_health_status()
    
    # 就绪检查：数据库必须健康，其他服务可以是警告状态
    database_healthy = False
    if "database" in health.services:
        database_healthy = health.services["database"].status in [HealthStatus.HEALTHY, HealthStatus.WARNING]
    
    if health.status in [HealthStatus.HEALTHY, HealthStatus.WARNING] and database_healthy:
        return {
            "status": "ready",
            "service": "topic_service",
            "timestamp": health.timestamp
        }
    else:
        return {
            "status": "not_ready",
            "service": "topic_service",
            "message": health.message,
            "timestamp": health.timestamp
        }


if __name__ == "__main__":
    import asyncio
    
    async def test_health_check():
        """测试健康检查功能"""
        print("🧪 测试主题服务健康检查...")
        
        checker = get_health_checker()
        health = await checker.get_health_status()
        
        print(f"整体状态: {health.status}")
        print(f"服务名称: {health.service_name}")
        print(f"版本: {health.version}")
        print(f"运行时间: {health.uptime_seconds:.2f}秒")
        print(f"消息: {health.message}")
        
        print("\n服务检查结果:")
        for name, service in health.services.items():
            print(f"  {name}: {service.status} ({service.response_time_ms:.2f}ms)")
            if service.message:
                print(f"    消息: {service.message}")
        
        print(f"\n系统状态:")
        print(f"  CPU: {health.system.cpu_percent:.1f}%")
        print(f"  内存: {health.system.memory_percent:.1f}%")
        print(f"  磁盘: {health.system.disk_percent:.1f}%")
        print(f"  系统状态: {health.system.status}")
        
        # 测试就绪状态
        ready = await get_ready_status()
        print(f"\n就绪状态: {ready['status']}")
    
    asyncio.run(test_health_check())
