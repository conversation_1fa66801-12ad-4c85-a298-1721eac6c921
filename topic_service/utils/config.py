"""
主题服务配置管理

基于全局配置模板的主题服务专用配置
"""

from pydantic import Field, validator
from typing import Optional, List
from functools import lru_cache
import sys
import os

# 添加项目根目录到路径，以便导入全局配置模板
sys.path.append(os.path.join(os.path.dirname(__file__), '../../'))
from scripts.config.config_template import BaseServiceSettings, DatabaseMixin, ExternalServiceMixin


class TopicServiceSettings(BaseServiceSettings, DatabaseMixin, ExternalServiceMixin):
    """主题服务配置类"""
    
    # ===== 服务特定配置 =====
    api_port: int = Field(
        default=9004,
        description="主题服务端口"
    )
    api_title: str = Field(
        default="Topic Service API",
        description="主题服务API标题"
    )
    api_description: str = Field(
        default="主题管理和文档关联服务",
        description="主题服务API描述"
    )
    
    # ===== 外部服务依赖 =====
    user_service_url: str = Field(
        default="http://localhost:9002",
        description="用户服务URL"
    )
    document_service_url: str = Field(
        default="http://localhost:9005",
        description="文档服务URL"
    )
    embedding_service_url: str = Field(
        default="http://localhost:9001",
        description="向量化服务URL"
    )
    
    # ===== 业务配置 =====
    max_topics_per_user: int = Field(
        default=100,
        ge=1, le=1000,
        description="每个用户最大主题数"
    )
    max_documents_per_topic: int = Field(
        default=50,
        ge=1, le=500,
        description="每个主题最大文档数"
    )
    topic_name_max_length: int = Field(
        default=100,
        ge=10, le=255,
        description="主题名称最大长度"
    )
    topic_description_max_length: int = Field(
        default=1000,
        ge=50, le=5000,
        description="主题描述最大长度"
    )
    
    # ===== 搜索配置 =====
    enable_topic_search: bool = Field(
        default=True,
        description="是否启用主题搜索"
    )
    search_results_limit: int = Field(
        default=20,
        ge=5, le=100,
        description="搜索结果数量限制"
    )
    search_timeout: int = Field(
        default=5,
        ge=1, le=30,
        description="搜索超时时间(秒)"
    )
    
    # ===== 缓存配置 =====
    enable_topic_cache: bool = Field(
        default=True,
        description="是否启用主题缓存"
    )
    topic_cache_ttl: int = Field(
        default=3600,  # 1小时
        ge=300, le=86400,  # 5分钟到24小时
        description="主题缓存过期时间(秒)"
    )
    popular_topics_cache_ttl: int = Field(
        default=1800,  # 30分钟
        ge=300, le=7200,  # 5分钟到2小时
        description="热门主题缓存过期时间(秒)"
    )
    
    # ===== 统计配置 =====
    enable_statistics: bool = Field(
        default=True,
        description="是否启用统计功能"
    )
    statistics_update_interval: int = Field(
        default=300,  # 5分钟
        ge=60, le=3600,  # 1分钟到1小时
        description="统计更新间隔(秒)"
    )
    
    # ===== 权限配置 =====
    enable_topic_sharing: bool = Field(
        default=True,
        description="是否启用主题分享"
    )
    default_topic_visibility: str = Field(
        default="private",
        description="默认主题可见性"
    )
    
    @validator('user_service_url', 'document_service_url', 'embedding_service_url')
    def validate_service_urls(cls, v):
        if not v.startswith(('http://', 'https://')):
            raise ValueError("服务URL必须以http://或https://开头")
        return v.rstrip('/')
    
    @validator('default_topic_visibility')
    def validate_topic_visibility(cls, v):
        valid_visibility = ['private', 'public', 'shared']
        if v not in valid_visibility:
            raise ValueError(f'主题可见性必须是以下之一: {valid_visibility}')
        return v
    
    class Config:
        env_prefix = "TOPIC_"
        env_file = ".env"
        case_sensitive = False


@lru_cache()
def get_settings() -> TopicServiceSettings:
    """获取主题服务配置实例（单例模式）"""
    from scripts.config.config_template import get_service_settings
    return get_service_settings(TopicServiceSettings)


def create_env_example() -> str:
    """创建主题服务的.env.example文件内容"""
    from scripts.config.config_template import create_env_template
    
    additional_vars = {
        # 数据库配置
        "DATABASE_URL": "postgresql://master_know_user:master_know_pass@localhost:5432/master_know",
        "DATABASE_POOL_SIZE": "10",
        "DATABASE_POOL_TIMEOUT": "30",
        "DATABASE_ECHO": "false",
        
        # 外部服务依赖
        "USER_SERVICE_URL": "http://localhost:9002",
        "DOCUMENT_SERVICE_URL": "http://localhost:9005",
        "EMBEDDING_SERVICE_URL": "http://localhost:9001",
        
        # 业务配置
        "MAX_TOPICS_PER_USER": "100",
        "MAX_DOCUMENTS_PER_TOPIC": "50",
        "TOPIC_NAME_MAX_LENGTH": "100",
        "TOPIC_DESCRIPTION_MAX_LENGTH": "1000",
        
        # 搜索配置
        "ENABLE_TOPIC_SEARCH": "true",
        "SEARCH_RESULTS_LIMIT": "20",
        "SEARCH_TIMEOUT": "5",
        
        # 缓存配置
        "ENABLE_TOPIC_CACHE": "true",
        "TOPIC_CACHE_TTL": "3600",
        "POPULAR_TOPICS_CACHE_TTL": "1800",
        
        # 统计配置
        "ENABLE_STATISTICS": "true",
        "STATISTICS_UPDATE_INTERVAL": "300",
        
        # 权限配置
        "ENABLE_TOPIC_SHARING": "true",
        "DEFAULT_TOPIC_VISIBILITY": "private",
    }
    
    return create_env_template("Topic Service", "TOPIC", additional_vars)


if __name__ == "__main__":
    # 测试配置加载
    try:
        settings = get_settings()
        print("✅ 主题服务配置加载成功")
        print(f"API端口: {settings.api_port}")
        print(f"数据库URL: {settings.database_url}")
        print(f"用户服务URL: {settings.user_service_url}")
        print(f"最大主题数: {settings.max_topics_per_user}")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
    
    # 生成.env.example文件
    env_content = create_env_example()
    with open("topic_service/.env.example", "w", encoding="utf-8") as f:
        f.write(env_content)
    print("✅ 已生成 topic_service/.env.example 文件")
