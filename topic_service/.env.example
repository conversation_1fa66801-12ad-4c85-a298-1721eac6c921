# Topic Service Configuration

# ===== API服务配置 =====
TOPIC_API_HOST=0.0.0.0
TOPIC_API_PORT=9004
TOPIC_API_TITLE="Topic Service API"
TOPIC_API_DESCRIPTION="主题管理和文档关联服务"
TOPIC_API_VERSION=1.0.0
TOPIC_API_PREFIX=/api/v1

# ===== 日志配置 =====
TOPIC_LOG_LEVEL=INFO
TOPIC_LOG_FORMAT=json
TOPIC_ENABLE_REQUEST_LOGGING=true

# ===== 健康检查配置 =====
TOPIC_HEALTH_CHECK_TIMEOUT=5
TOPIC_ENABLE_HEALTH_CHECK=true

# ===== 安全配置 =====
TOPIC_ENABLE_CORS=true
TOPIC_CORS_ORIGINS=["*"]

# ===== 环境配置 =====
TOPIC_ENVIRONMENT=development
TOPIC_DEBUG=false

# ===== 外部服务配置 =====
TOPIC_REQUEST_TIMEOUT=30
TOPIC_MAX_RETRIES=3
TOPIC_RETRY_DELAY=1.0

# ===== 服务特定配置 =====
# 数据库配置
TOPIC_DATABASE_URL=postgresql://master_know_user:master_know_pass@localhost:5432/master_know
TOPIC_DATABASE_POOL_SIZE=10
TOPIC_DATABASE_POOL_TIMEOUT=30
TOPIC_DATABASE_ECHO=false

# 外部服务依赖
TOPIC_USER_SERVICE_URL=http://localhost:9002
TOPIC_DOCUMENT_SERVICE_URL=http://localhost:9005
TOPIC_EMBEDDING_SERVICE_URL=http://localhost:9001

# 业务配置
TOPIC_MAX_TOPICS_PER_USER=100
TOPIC_MAX_DOCUMENTS_PER_TOPIC=50
TOPIC_TOPIC_NAME_MAX_LENGTH=100
TOPIC_TOPIC_DESCRIPTION_MAX_LENGTH=1000

# 搜索配置
TOPIC_ENABLE_TOPIC_SEARCH=true
TOPIC_SEARCH_RESULTS_LIMIT=20
TOPIC_SEARCH_TIMEOUT=5

# 缓存配置
TOPIC_ENABLE_TOPIC_CACHE=true
TOPIC_TOPIC_CACHE_TTL=3600
TOPIC_POPULAR_TOPICS_CACHE_TTL=1800

# 统计配置
TOPIC_ENABLE_STATISTICS=true
TOPIC_STATISTICS_UPDATE_INTERVAL=300

# 权限配置
TOPIC_ENABLE_TOPIC_SHARING=true
TOPIC_DEFAULT_TOPIC_VISIBILITY=private
