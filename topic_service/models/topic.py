from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class TopicBase(BaseModel):
    title: str
    description: Optional[str] = None

class TopicCreate(TopicBase):
    user_id: int = 1  # 固定测试用户

class Topic(TopicBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime
    is_active: bool = True
    document_count: int = 0

class TopicResponse(BaseModel):
    topics: list[Topic]
    total: int
    limit: int
    offset: int
